﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace unitededisprinter.service
{
    public class Roles1
    {
        public bool checkrole(string username, string pass, string pageid)
        {
            string issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT  roleid from login where users =@users and password=@password", con1);
            cmd7.Parameters.AddWithValue("@users", username);
            cmd7.Parameters.AddWithValue("@password", pass);


            using (con1)
            {
                con1.Open();
                object x1= cmd7.ExecuteScalar();
                if (x1 != null) { issub1 = x1.ToString(); } else { issub1 = " "; }
               
                con1.Close();
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            con.Open();
            SqlCommand cmd = new SqlCommand("select * from sitNetRolespages where roleid=@roleid and pageid=@pageid", con);
            cmd.Parameters.AddWithValue("@roleid", issub1);
            cmd.Parameters.AddWithValue("@pageid", pageid);
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            da.Fill(dt);
            if (dt.Rows.Count > 0)
            {
                return true;
            }
            else
            {
                return false;

            }

        }
        }
}