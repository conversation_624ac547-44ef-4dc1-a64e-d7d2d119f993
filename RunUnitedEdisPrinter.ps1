# United EDIS Printer Launcher Script
# This script launches the United EDIS Printer MAUI application

Write-Host "United EDIS Printer Application Launcher" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""

# Define paths
$exePath = "UnitedEdisPrinterMAUI\publish\UnitedEdisPrinterMAUI.exe"
$projectPath = "UnitedEdisPrinterMAUI\UnitedEdisPrinterMAUI.csproj"

# Check if executable exists
if (-not (Test-Path $exePath)) {
    Write-Host "ERROR: Application executable not found!" -ForegroundColor Red
    Write-Host "Path: $exePath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "The application needs to be published first." -ForegroundColor Yellow
    Write-Host ""
    
    # Check if project exists
    if (Test-Path $projectPath) {
        Write-Host "Would you like to build and publish the application now? (Y/N)" -ForegroundColor Cyan
        $response = Read-Host
        
        if ($response -eq "Y" -or $response -eq "y") {
            Write-Host "Building and publishing application..." -ForegroundColor Yellow
            
            try {
                Set-Location "UnitedEdisPrinterMAUI"
                dotnet publish -c Release -f net8.0-windows10.0.19041.0 --self-contained true -r win-x64 -o publish
                Set-Location ".."
                
                if (Test-Path $exePath) {
                    Write-Host "Application published successfully!" -ForegroundColor Green
                } else {
                    Write-Host "Failed to publish application." -ForegroundColor Red
                    Read-Host "Press Enter to exit"
                    exit 1
                }
            }
            catch {
                Write-Host "Error during build: $($_.Exception.Message)" -ForegroundColor Red
                Read-Host "Press Enter to exit"
                exit 1
            }
        } else {
            Write-Host "Please run the following command to publish the application:" -ForegroundColor Yellow
            Write-Host "dotnet publish -c Release -f net8.0-windows10.0.19041.0 --self-contained true -r win-x64 -o publish" -ForegroundColor Cyan
            Read-Host "Press Enter to exit"
            exit 1
        }
    } else {
        Write-Host "Project file not found: $projectPath" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Launch the application
try {
    Write-Host "Launching United EDIS Printer Application..." -ForegroundColor Green
    Start-Process -FilePath $exePath -WorkingDirectory (Split-Path $exePath -Parent)
    Write-Host "Application started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "The application is now running. You can close this window." -ForegroundColor Cyan
}
catch {
    Write-Host "Error launching application: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Optional: Keep window open for a few seconds
Start-Sleep -Seconds 3
