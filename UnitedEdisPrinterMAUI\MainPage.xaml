﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:UnitedEdisPrinterMAUI.ViewModels"
             x:Class="UnitedEdisPrinterMAUI.MainPage"
             Title="United EDIS Printer"
             BackgroundColor="White">

    <ContentPage.Resources>
        <ResourceDictionary>
            <!-- Converters -->
            <vm:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <vm:StringToBoolConverter x:Key="StringToBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid>
        <!-- Main Content -->
        <ScrollView>
            <Grid RowDefinitions="*,Auto,*" Padding="20">

                <!-- Loading Indicator -->
                <ActivityIndicator Grid.Row="1"
                                 IsVisible="{Binding IsSearching}"
                                 IsRunning="{Binding IsSearching}"
                                 Color="#9370DB"
                                 VerticalOptions="Center"
                                 HorizontalOptions="Center" />

                <!-- Main Input Section -->
                <StackLayout Grid.Row="1"
                           IsVisible="{Binding IsSearching, Converter={StaticResource InvertedBoolConverter}}"
                           VerticalOptions="Center"
                           HorizontalOptions="Center"
                           Spacing="20">

                    <!-- QR/Mobile Input Section -->
                    <StackLayout IsVisible="{Binding IsShowingResults, Converter={StaticResource InvertedBoolConverter}}"
                               Spacing="15">

                        <Entry x:Name="QrMobileEntry"
                               Text="{Binding QrOrMobileInput}"
                               Placeholder="QR Code Or Mobile Number"
                               FontSize="18"
                               HorizontalTextAlignment="Center"
                               BackgroundColor="White"
                               TextColor="Black"
                               IsReadOnly="True"
                               WidthRequest="400" />

                        <Label Text="QR Code Or Mobile Number"
                               FontSize="24"
                               HorizontalOptions="Center"
                               TextColor="Black" />

                        <!-- Calculator-style Keypad -->
                        <Grid RowDefinitions="Auto,Auto,Auto,Auto"
                              ColumnDefinitions="*,*,*"
                              RowSpacing="5"
                              ColumnSpacing="5"
                              WidthRequest="300"
                              HorizontalOptions="Center">

                            <!-- Row 1 -->
                            <Button Grid.Row="0" Grid.Column="0" Text="1"
                                    Command="{Binding AddDigitCommand}" CommandParameter="1"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="0" Grid.Column="1" Text="2"
                                    Command="{Binding AddDigitCommand}" CommandParameter="2"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="0" Grid.Column="2" Text="3"
                                    Command="{Binding AddDigitCommand}" CommandParameter="3"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />

                            <!-- Row 2 -->
                            <Button Grid.Row="1" Grid.Column="0" Text="4"
                                    Command="{Binding AddDigitCommand}" CommandParameter="4"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="1" Grid.Column="1" Text="5"
                                    Command="{Binding AddDigitCommand}" CommandParameter="5"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="1" Grid.Column="2" Text="6"
                                    Command="{Binding AddDigitCommand}" CommandParameter="6"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />

                            <!-- Row 3 -->
                            <Button Grid.Row="2" Grid.Column="0" Text="7"
                                    Command="{Binding AddDigitCommand}" CommandParameter="7"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="2" Grid.Column="1" Text="8"
                                    Command="{Binding AddDigitCommand}" CommandParameter="8"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="2" Grid.Column="2" Text="9"
                                    Command="{Binding AddDigitCommand}" CommandParameter="9"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />

                            <!-- Row 4 -->
                            <Button Grid.Row="3" Grid.Column="0" Text="Reset"
                                    Command="{Binding AddDigitCommand}" CommandParameter="Reset"
                                    BackgroundColor="#ff0000" TextColor="White" FontSize="16" />
                            <Button Grid.Row="3" Grid.Column="1" Text="0"
                                    Command="{Binding AddDigitCommand}" CommandParameter="0"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="3" Grid.Column="2" Text="⌫"
                                    Command="{Binding AddDigitCommand}" CommandParameter="Backspace"
                                    BackgroundColor="#ff0000" TextColor="White" FontSize="20" />
                        </Grid>

                        <Button Text="Search"
                                Command="{Binding SearchCommand}"
                                BackgroundColor="#28a745"
                                TextColor="White"
                                FontSize="18"
                                WidthRequest="400"
                                HeightRequest="50" />
                    </StackLayout>

                    <!-- Search Results Section -->
                    <StackLayout IsVisible="{Binding IsShowingResults}"
                               Spacing="15">

                        <Label Text="Who Are You?"
                               FontSize="24"
                               HorizontalOptions="Center"
                               TextColor="Black" />

                        <CollectionView ItemsSource="{Binding SearchResults}"
                                      WidthRequest="500">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Grid ColumnDefinitions="*,Auto"
                                          Padding="10"
                                          BackgroundColor="#f8f9fa">
                                        <Label Grid.Column="0"
                                               Text="{Binding Applicant_Name__c}"
                                               FontSize="18"
                                               VerticalOptions="Center" />
                                        <Button Grid.Column="1"
                                                Text="I am"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.SelectApplicantCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="#007bff"
                                                TextColor="White" />
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <Button Text="Back To Home"
                                Command="{Binding BackToHomeCommand}"
                                BackgroundColor="#6c757d"
                                TextColor="White"
                                FontSize="16" />
                    </StackLayout>

                    <!-- Attendee Selection Section -->
                    <StackLayout IsVisible="{Binding IsShowingAttendeeSelection}"
                               Spacing="15">

                        <Frame BackgroundColor="#f4f4f4"
                               Padding="15"
                               CornerRadius="25"
                               WidthRequest="400">
                            <StackLayout>
                                <Label Text="{Binding SelectedApplicantName}"
                                       FontSize="20"
                                       FontAttributes="Bold"
                                       HorizontalOptions="Center" />
                                <Label Text="{Binding SelectedMobile}"
                                       FontSize="16"
                                       HorizontalOptions="Center" />
                            </StackLayout>
                        </Frame>

                        <Label Text="Please select the number of attendees"
                               FontSize="18"
                               HorizontalOptions="Center"
                               TextColor="#0b7cc0" />

                        <!-- Attendee Count Buttons -->
                        <Grid RowDefinitions="Auto,Auto,Auto"
                              ColumnDefinitions="*,*,*"
                              RowSpacing="10"
                              ColumnSpacing="10"
                              WidthRequest="300"
                              HorizontalOptions="Center">

                            <Button Grid.Row="0" Grid.Column="0" Text="1"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="1"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="0" Grid.Column="1" Text="2"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="2"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="0" Grid.Column="2" Text="3"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="3"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />

                            <Button Grid.Row="1" Grid.Column="0" Text="4"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="4"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="1" Grid.Column="1" Text="5"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="5"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="1" Grid.Column="2" Text="6"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="6"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />

                            <Button Grid.Row="2" Grid.Column="0" Text="7"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="7"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="2" Grid.Column="1" Text="8"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="8"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                            <Button Grid.Row="2" Grid.Column="2" Text="9"
                                    Command="{Binding PrintBadgesCommand}" CommandParameter="9"
                                    BackgroundColor="#0b7cc0" TextColor="White" FontSize="20" />
                        </Grid>

                        <Button Text="Back To Home"
                                Command="{Binding BackToHomeCommand}"
                                BackgroundColor="#6c757d"
                                TextColor="White"
                                FontSize="16"
                                Margin="0,20,0,0" />
                    </StackLayout>

                    <!-- Status Message -->
                    <Label Text="{Binding StatusMessage}"
                           FontSize="16"
                           HorizontalOptions="Center"
                           TextColor="Red"
                           IsVisible="{Binding StatusMessage, Converter={StaticResource StringToBoolConverter}}" />

                </StackLayout>
            </Grid>
        </ScrollView>
    </Grid>

</ContentPage>
