using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using UnitedEdisPrinterMAUI.Models;
using UnitedEdisPrinterMAUI.Services;

namespace UnitedEdisPrinterMAUI.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly SalesforceService _salesforceService;
    private readonly PrinterService _printerService;

    [ObservableProperty]
    private string qrOrMobileInput = string.Empty;

    [ObservableProperty]
    private bool isSearching = false;

    [ObservableProperty]
    private bool isShowingResults = false;

    [ObservableProperty]
    private bool isShowingAttendeeSelection = false;

    [ObservableProperty]
    private bool isPrinting = false;

    [ObservableProperty]
    private string selectedLeadId = string.Empty;

    [ObservableProperty]
    private string selectedApplicantName = string.Empty;

    [ObservableProperty]
    private string selectedMobile = string.Empty;

    [ObservableProperty]
    private string statusMessage = string.Empty;

    public ObservableCollection<Record1> SearchResults { get; } = new();

    public MainViewModel(SalesforceService salesforceService, PrinterService printerService)
    {
        _salesforceService = salesforceService;
        _printerService = printerService;
    }

    [RelayCommand]
    private async Task SearchAsync()
    {
        if (string.IsNullOrWhiteSpace(QrOrMobileInput))
        {
            StatusMessage = "Please enter QR Code or Mobile Number";
            return;
        }

        IsSearching = true;
        StatusMessage = "Searching...";
        SearchResults.Clear();

        try
        {
            List<Record1> results;

            // Check if input contains letters (QR code) or just numbers (mobile)
            if (HasLetters(QrOrMobileInput))
            {
                // QR Code - search by ID
                results = await _salesforceService.GetListStudentsAsync(QrOrMobileInput, 1, 16);
                if (results.Any())
                {
                    await _salesforceService.UpdateAttendanceAsync(QrOrMobileInput, "Attended");
                }
            }
            else
            {
                // Mobile number - search by mobile
                results = await _salesforceService.GetListStudentsByMobileAsync(QrOrMobileInput);
            }

            if (!results.Any())
            {
                StatusMessage = "No Record Found For This Number. Please Visit Our Registration Desk.";
                QrOrMobileInput = string.Empty;
                return;
            }

            foreach (var result in results)
            {
                SearchResults.Add(result);
            }

            if (results.Count == 1 && !string.IsNullOrEmpty(results[0].Applicant__c))
            {
                // Single result with applicant - go directly to attendee selection
                await SelectApplicantAsync(results[0]);
            }
            else
            {
                // Multiple results - show selection
                IsShowingResults = true;
                StatusMessage = "Select your record:";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error searching: {ex.Message}";
        }
        finally
        {
            IsSearching = false;
        }
    }

    [RelayCommand]
    private async Task SelectApplicantAsync(Record1 record)
    {
        SelectedLeadId = record.Id ?? string.Empty;
        SelectedApplicantName = record.Applicant_Name__c ?? string.Empty;
        SelectedMobile = record.Mobile__c ?? string.Empty;

        IsShowingResults = false;
        IsShowingAttendeeSelection = true;
        StatusMessage = "Please select the number of attendees";
    }

    [RelayCommand]
    private async Task PrintBadgesAsync(int attendeeCount)
    {
        if (string.IsNullOrEmpty(SelectedLeadId))
        {
            StatusMessage = "No record selected";
            return;
        }

        var confirmed = await Shell.Current.DisplayAlert(
            "Confirm",
            $"Is the number of attendees with you {attendeeCount}?",
            "Yes",
            "No");

        if (!confirmed)
        {
            StatusMessage = "Please choose the number of attendees with you";
            return;
        }

        IsPrinting = true;
        StatusMessage = "Printing badges...";

        try
        {
            var result = await _printerService.PrintBadgesAsync(SelectedLeadId, attendeeCount);
            
            if (result == "D")
            {
                StatusMessage = "Badges printed successfully!";
                await Task.Delay(2000); // Show success message for 2 seconds
                ResetToHome();
            }
            else
            {
                StatusMessage = $"Printing failed: {result}";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error printing: {ex.Message}";
        }
        finally
        {
            IsPrinting = false;
        }
    }

    [RelayCommand]
    private void AddDigit(string digit)
    {
        if (digit == "Reset")
        {
            QrOrMobileInput = string.Empty;
        }
        else if (digit == "Backspace")
        {
            if (QrOrMobileInput.Length > 0)
            {
                QrOrMobileInput = QrOrMobileInput[..^1];
            }
        }
        else
        {
            QrOrMobileInput += digit;
        }
    }

    [RelayCommand]
    private void BackToHome()
    {
        ResetToHome();
    }

    private void ResetToHome()
    {
        QrOrMobileInput = string.Empty;
        SelectedLeadId = string.Empty;
        SelectedApplicantName = string.Empty;
        SelectedMobile = string.Empty;
        StatusMessage = string.Empty;
        IsShowingResults = false;
        IsShowingAttendeeSelection = false;
        SearchResults.Clear();
    }

    private static bool HasLetters(string content)
    {
        return content.Any(char.IsLetter);
    }
}
