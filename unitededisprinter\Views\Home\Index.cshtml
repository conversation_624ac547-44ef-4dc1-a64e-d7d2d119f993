﻿
@{
    ViewBag.Title = "Index";
}
<div id="progress" class="modal1" style="display:none">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    /* أسلوب لإخفاء النموذج */
    #barcodeForm {
        opacity: 1;
        transition: opacity 0.5s ease-out; /* مدة الانتقال بوحدة ثانية ونمط الانتقال */
    }

    .hidden {
        opacity: 0;
    }

    .show1 {
        opacity: 1;
        transition: opacity 0.5s ease-out; /* مدة الانتقال بوحدة ثانية ونمط الانتقال */
    }

    .calculator {
       
        width: 300px;
        margin: 15px auto;
        padding: 20px;
        background-color: #f4f4f4;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .display {
        width: calc(100% - 10px);
        padding: 10px;
        margin-bottom: 10px;
        font-size: 24px;
        border: none;
        border-radius: 5px;
    }

    .buttons {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;
    }

    button,a {
        padding: 15px;
        font-size: 20px;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        background-color: #ddd;
        z-index: 3;
    }
    a {
        text-decoration: none;
    }
    .number, .operator {
        background-color: #0b7cc0;
        color: #fff
    }
    .numberd {
        background-color: #ff0000;
        color: #fff
    }
    .calculate {
        background-color: #ff5722;
        color: #fff;
    }

   

    .modal1 {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        background-color: white;
        z-index: 5;
        opacity: 0.5;
        filter: alpha(opacity=50);
        -moz-opacity: 0.5;
        min-height: 100%;
        width: 100%;
    }

    .throbber {
        display: block;
        z-index: 6
    }

        .throbber .curtain {
            background-color: #fff;
            filter: alpha(opacity=60);
            height: 100%;
            left: 0;
            opacity: 0.6;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 99999999999
        }

        .throbber .curtain-content {
            height: 50%;
            left: 50%;
            position: absolute;
            top: 50%;
            width: 50%;
            z-index: 99999999999
        }

            .throbber .curtain-content div {
                color: #FFF;
                text-align: center;
                z-index: 99999999999
            }



    #preloader {
        position: fixed;
        top: 50%;
        left: 50%;
        z-index: 99999999999
    }

    #loader {
        z-index: 99999999999;
        display: block;
        position: relative;
        left: 50%;
        top: 50%;
        width: 150px;
        height: 150px;
        margin: -75px 0 0 -75px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #9370DB;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
        opacity: 0.9;
        filter: alpha(opacity=90);
        -moz-opacity: 0.9;
    }

        #loader:before {
            width: 150px;
            height: 150px;
            content: "";
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #BA55D3;
            -webkit-animation: spin 3s linear infinite;
            animation: spin 3s linear infinite;
            opacity: 0.9;
            filter: alpha(opacity=90);
            -moz-opacity: 0.9;
            z-index: 9999999
        }

        #loader:after {
            width: 150px;
            height: 150px;
            content: "";
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #FF00FF;
            -webkit-animation: spin 1.5s linear infinite;
            animation: spin 1.5s linear infinite;
            opacity: 0.9;
            filter: alpha(opacity=90);
            -moz-opacity: 0.9;
            z-index: 9999999
        }

    @@-webkit-keyframes spin {
        0% {
            -webkit-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            transform: rotate(0deg);
        }

        100% {
            -webkit-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }

    @@keyframes spin {
        0% {
            -webkit-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            transform: rotate(0deg);
        }

        100% {
            -webkit-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }
</style>
<img src="~/images/nnfooter.png" style="position:absolute;bottom:100px;left:100px;width:200px" 
<div class="row">
    <div class="col-md-12">

        <div class="row">
            <div class="col-md-2"></div>
            <div class="col-md-8" style="  position: relative; height: 700px;">
                <div id="first" class="form-group" style=" margin: 0; position: absolute; top: 35%; left: 50%; transform: translate(-50%, -50%);width:80%;z-index:4">

                    <form id="barcodeForm" onsubmit="validateBarcode(event)">



                        <input type="text" class="form-control" id="qrmnnumber" style="width:100%!important" placeholder="QR Code Or Mobile Number" inputmode='none' autocomplete="off">
                        <label for="exampleInputEmail1" style="font-size:32px">QR Code Or Mobile Number</label>


                        <div class="calculator" id="second22">

                            <div class="buttons">
                                <a class="number nn" onclick="printpgmobile(1)">1</a>
                                <a class="number nn" onclick="printpgmobile(2)">2</a>
                                <a class="number nn" onclick="printpgmobile(3)">3</a>


                                <a class="number nn" onclick="printpgmobile(4)">4</a>
                                <a class="number nn" onclick="printpgmobile(5)">5</a>
                                <a class="number nn" onclick="printpgmobile(6)">6</a>

                                <a class="number nn" onclick="printpgmobile(7)">7</a>
                                <a class="number nn" onclick="printpgmobile(8)">8</a>
                                <a class="number nn" onclick="printpgmobile(9)">9</a>
                                <a class="numberd nn " onclick="printpgmobile(100)">Reset</a>

                                <a class="number nn" onclick="printpgmobile(0)">0</a>

                                <a class="numberd nn" onclick="printpgmobile(99)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-backspace-fill" viewBox="0 0 16 16">
                                        <path d="M15.683 3a2 2 0 0 0-2-2h-7.08a2 2 0 0 0-1.519.698L.241 7.35a1 1 0 0 0 0 1.302l4.843 5.65A2 2 0 0 0 6.603 15h7.08a2 2 0 0 0 2-2zM5.829 5.854a.5.5 0 1 1 .707-.708l2.147 2.147 2.146-2.147a.5.5 0 1 1 .707.708L9.39 8l2.146 2.146a.5.5 0 0 1-.707.708L8.683 8.707l-2.147 2.147a.5.5 0 0 1-.707-.708L7.976 8z" />
                                    </svg>
                                </a>
                            </div>


                        </div>
                        <input class="btn btn-success btn-lg btn-block" type="submit" value="Search" style="width:100%;">
                    </form>

                </div>
                <div id="second" class="form-group hidden" style=" margin: 0; position: absolute; top: 40%; left: 50%; transform: translate(-50%, -50%); width: 80%; z-index: 2;">
                    <div id="second1" style="border-radius:5px 50px; border:solid 1px rgba(0, 0, 0, 0.1);background-color:#f4f4f4;margin-bottom:15px;padding:15px">

                    </div>
                    <h4 style="color: #0b7cc0">Please select the number of attendees</h4>
                    <div class="calculator" id="second2">

                        <div class="buttons">
                            <button class="number" onclick="printpg(1)">1</button>
                            <button class="number" onclick="printpg(2)">2</button>
                            <button class="number" onclick="printpg(3)">3</button>


                            <button class="number" onclick="printpg(4)">4</button>
                            <button class="number" onclick="printpg(5)">5</button>
                            <button class="number" onclick="printpg(6)">6</button>

                            <button class="number" onclick="printpg(7)">7</button>
                            <button class="number" onclick="printpg(8)">8</button>
                            <button class="number" onclick="printpg(9)">9</button>

                        </div>

                    </div><br/>
                    <a class="numberd nn" onclick="back();" style="margin-top:25px">Back To Home</a>
                </div>
            </div>
            <div class="col-md-8"></div>
        </div>
        <input type="text" class="form-control" id="qrmnnumberc" style="display:none" placeholder="a1">

    </div>
    </div>
    <style>
        /* The Modal (background) */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 999; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgb(0,0,0); /* Fallback color */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
        }

        /* Modal Content/Box */
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto; /* 15% from the top and centered */
            padding: 20px;
            border: 1px solid #888;
            width: 80%; /* Could be more or less, depending on screen size */
        }

        /* The Close Button */
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

            .close:hover,
            .close:focus {
                color: black;
                text-decoration: none;
                cursor: pointer;
            }
        .kanban-item{
            font-size:24px;
            font-weight:700
        }
    </style>
    @section Scripts {
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

   

        <script>
            document.addEventListener("contextmenu", function (e) {
                e.preventDefault();
            }, false);</script>


        <script>
            function back() {
                location.reload();
            }
            function validateBarcode1(barcodeValue) {
                var modal = document.getElementById("myModal");

                modal.style.display = "none";
                $('#progress').show();
                $.getJSON("/home/<USER>", { pageNumber: '2', pageSize: '16', lstate: barcodeValue }, function (response) {

                    let Id = "";
                    var rowData = "";
                    for (var i = 0; i < response.length; i++) {

                        let fir = response[i].Applicant_Name__c;
                        let mob = response[i].Mobile__c;
                         Id = response[i].Id;





                        rowData = rowData + '<div class="kanban-item">' + fir + '</div><div class="kanban-item" >' + mob + '</div>';
                        $("#second1").empty();
                        $("#first").empty();
                        $("#second1").append(rowData);
                        $("#second2").show();

                       
                    }
                    document.getElementById("qrmnnumberc").value = barcodeValue;
                    document.getElementById("first").classList.add("hidden");
                    $('#progress').hide();
                    document.getElementById("second").classList.remove("hidden");
                    document.getElementById("second").classList.add("show");
                   

                });
            }
            function validateBarcode(event) {
                event.preventDefault(); // منع تقديم النموذج بشكل افتراضي
                clearTimeout(typingTimer);
                var barcodeValue = document.getElementById("qrmnnumber").value;
                if (barcodeValue == "") {
                    Swal.fire("Error!", "Please read Qr Code or Write your Registred Mobile Number", "error");
                } else {
                    $('#progress').show();
                }
                document.getElementById("qrmnnumberc").value = barcodeValue;
                $.getJSON("/home/<USER>", { pageNumber: '1', pageSize: '16', lstate: barcodeValue }, function (response) {

                    var appid = response;

                    if (appid === "0") {
                        Swal.fire("Error!", "No Record Found For This Number<br> Please Visit Our Registration Desk.", "error");
                        document.getElementById("qrmnnumber").value = "";
                        $('#progress').hide();


                    } else {
                        if (response[0].Applicant__c != null) {
                            if (response.length == 1) {
                                for (var i = 0; i < response.length; i++) {

                                    let fir = response[i].Applicant_Name__c;
                                    let link = response[i].Id;

                                    validateBarcode1(link);




                                }



                            } else {
                                var rowData = '<table class="table table-striped">';
                                for (var i = 0; i < response.length; i++) {

                                    let fir = response[i].Applicant_Name__c;
                                    let link = response[i].Id;






                                    rowData = rowData + '<tr><td>' + fir + '</td>' + '<td><a class="btn btn-primary"  onclick="validateBarcode1(\'' + link + '\')">I am</a></td></tr>';
                                }
                                rowData = rowData + '</table>';
                                var modal = document.getElementById("myModal");
                                $('#progress').hide();
                                $("#content").append(rowData);
                                modal.style.display = "block";
                            }


                        } else {
                            var rowData = "";
                            for (var i = 0; i < response.length; i++) {

                                let fir = response[i].Applicant_Name__c;
                                let mob = response[i].Mobile__c;
                                let link = response[i].Id;





                                rowData = rowData + '<div class="kanban-item">' + fir + '</div><div class="kanban-item" >' + mob + '</div>';
                                $("#second1").empty();
                                $("#first").empty();
                                $("#second1").append(rowData);
                                $("#second2").show();

                                document.getElementById("first").classList.add("hidden");
                                $('#progress').hide();
                                document.getElementById("second").classList.remove("hidden");
                                document.getElementById("second").classList.add("show");
                            }
                        }
                    }
                });




            }
            function deleteLastCharacterAndUpdate(inputId) {
                // Get the input element
                var inputElement = document.getElementById(inputId);

                // Get the current value
                var currentValue = inputElement.value;

                // Delete the last character
                var newValue = currentValue.slice(0, -1);

                // Update the input value
                inputElement.value = newValue;
            }
            var typingTimer; // تهيئة المتغير الذي سيستخدم للمؤقت

            function printpgmobile(par) {
                // إذا تم النقر على زر إعادة التعيين
                if (par == 100) {
                    document.getElementById("qrmnnumber").value = "";
                    clearTimeout(typingTimer); // مسح المؤقت
                }
                // إذا تم النقر على زر المسح
                else if (par == 99) {
                    deleteLastCharacterAndUpdate("qrmnnumber");
                    clearTimeout(typingTimer); // مسح المؤقت
                }
                // لجميع الحالات الأخرى
                else {
                    document.getElementById("qrmnnumber").value += par;
                    clearTimeout(typingTimer); // مسح المؤقت الحالي
                    typingTimer = setTimeout(clearText, 5000); // إعادة تعيين المؤقت ليمحو المحتوى بعد 5 ثواني
                }
            }

            // تابع لمسح محتوى حقل النص
            function clearText() {
                document.getElementById("qrmnnumber").value = ''; // مسح محتوى حقل النص
            }

        

           
            function printpg(par) {

                let pre = par;
                Swal.fire({
                    title: "Is the number of attendees with you " + pre + " ?",

                    showCancelButton: true,
                    confirmButtonText: "Yes",
                    cancelButtonText: `No`
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        $('#progress').show();
                        var barcodeValue = document.getElementById("qrmnnumberc").value;
                        $.getJSON("/home/<USER>", { leadid: barcodeValue, count1: par }, function (response) {
                            var appid = response;

                            if (appid === "D") {
                                $('#progress').hide();
                                document.getElementById("qrmnnumberc").value = "";
                              
                                window.location.reload();
                         
                            } else {
                                $('#progress').hide();
                                Swal.fire("Error!", response, "error");
                            }


                        });

                    } else {
                        Swal.fire("Please choose the number of attendees with you", "", "info");
                    }
                });


            }
        </script>
        <script type="text/javascript">
            var myInput = document.getElementById('qrmnnumber');
            setInterval(function () {
                // العثور على العنصر المطلوب بواسطة id


                // تحديد الفوكس على حقل النص
                myInput.focus();
            }, 1000); // تكرار كل 5000 مللي ثانية (5 ثواني)



        </script>

    }

    <!-- The Modal -->
    <div id="myModal" class="modal">
        <span class="close">&times;</span>

        <div class="modal-content">
            <h2 style="color:aquamarine">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" class="bi bi-info-circle" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                    <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0" />
                </svg>
            </h2>
            <h4>Who Are You?</h4>
            <div id="content">



            </div>
         

        </div>

    </div>
