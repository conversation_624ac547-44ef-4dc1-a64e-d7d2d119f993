﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Cryptography;
using System.Text;
using System.Configuration;

using System.Web.Configuration;

using System.IO;

using System.Net.Http;
using System.Net;
using Newtonsoft.Json;
using System.Data.SqlClient;
using Newtonsoft.Json.Linq;
using unitededisprinter.Models;

namespace unitededisprinter.service
{



    public class RsaHelper
    {


        private static readonly string Key = "HwUnitedGroup@20";
        public string Encrypt2023(string plainText)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
            byte[] ivBytes;

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV(); // Generate a random IV
                ivBytes = aes.IV;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                byte[] encryptedBytes = null;

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                        cs.Write(plainBytes, 0, plainBytes.Length);
                    }
                    encryptedBytes = ms.ToArray();
                }

                // Combine IV and encrypted data for storage
                byte[] combinedBytes = new byte[ivBytes.Length + encryptedBytes.Length];
                Buffer.BlockCopy(ivBytes, 0, combinedBytes, 0, ivBytes.Length);
                Buffer.BlockCopy(encryptedBytes, 0, combinedBytes, ivBytes.Length, encryptedBytes.Length);

                return Convert.ToBase64String(combinedBytes);
            }
        }
        public string Decrypt2023(string encryptedText)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
            byte[] combinedBytes = Convert.FromBase64String(encryptedText);

            byte[] ivBytes = new byte[16];
            byte[] encryptedBytes = new byte[combinedBytes.Length - ivBytes.Length];

            Buffer.BlockCopy(combinedBytes, 0, ivBytes, 0, ivBytes.Length);
            Buffer.BlockCopy(combinedBytes, ivBytes.Length, encryptedBytes, 0, encryptedBytes.Length);

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = ivBytes;
                aes.Padding = PaddingMode.PKCS7; // Set the padding mode explicitly

                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                byte[] decryptedBytes = null;

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream(encryptedBytes))
                using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (System.IO.MemoryStream msDecrypt = new System.IO.MemoryStream())
                {
                    int read;
                    byte[] buffer = new byte[1024];

                    while ((read = cs.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        msDecrypt.Write(buffer, 0, read);
                    }

                    decryptedBytes = msDecrypt.ToArray();
                }

                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }
    
        public string GetAccessToken()
        {// Get the record from the database by id
          

            // Check if the datetime field is older than 24 hours
            if (HttpContext.Current.Session["token"]==null)
            {
                string tokenEndpoint = "https://login.salesforce.com/services/oauth2/token";
                string grantType = "password";
                string clientId = "cid";
                string clientSecret = "sid";
                string username = "<EMAIL>";
                string password = "yhyasoft";
                string token = "";
                DateTime tokendate = new DateTime();
                DateTime now = System.DateTime.Now;
                SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);


                SqlCommand cmd6 = new SqlCommand("SELECT email FROM emails WHERE type=@type", con);
                SqlCommand cmd5 = new SqlCommand("SELECT password FROM emails WHERE type=@type", con);
                SqlCommand cmd4 = new SqlCommand("SELECT subject FROM emails WHERE type=@type", con);
                SqlCommand cmd3 = new SqlCommand("SELECT subject FROM emails WHERE type=@type", con);
                SqlCommand cmd33 = new SqlCommand("SELECT password FROM emails WHERE type=@type", con);
                SqlCommand cmd7 = new SqlCommand("SELECT subsubject FROM emails WHERE type=@type", con);
                SqlCommand cmd2 = new SqlCommand("SELECT mface FROM emails WHERE type=@type", con);


                cmd6.Parameters.AddWithValue("@type", "tokeng");
                cmd5.Parameters.AddWithValue("@type", "tokeng");
                cmd4.Parameters.AddWithValue("@type", "tokeng");
                cmd7.Parameters.AddWithValue("@type", "tokeng");
                cmd2.Parameters.AddWithValue("@type", "tokeng");
                cmd3.Parameters.AddWithValue("@type", "token");
                cmd33.Parameters.AddWithValue("@type", "token");




                using (con)
                {
                    con.Open();

                    username = Convert.ToString(cmd6.ExecuteScalar());

                    password = Decrypt2023(Convert.ToString(cmd5.ExecuteScalar())) + Decrypt2023(Convert.ToString(cmd2.ExecuteScalar()));

                    clientId = Convert.ToString(cmd4.ExecuteScalar());

                    object result = cmd3.ExecuteScalar();


                    //if (result != DBNull.Value)
                    //{
                    //    tokendate = Convert.ToDateTime(result);
                    //    token = Decrypt2023(Convert.ToString(cmd33.ExecuteScalar()));
                    //}
                    //else
                    //{
                    tokendate = DateTime.Today.AddDays(-1);
                    // }


                    clientSecret = Decrypt2023(Convert.ToString(cmd7.ExecuteScalar()));


                }
                TimeSpan difference = now - tokendate;
                if (difference < TimeSpan.FromHours(23))

                {
                    return token;
                }
                else
                {
                    ServicePointManager.Expect100Continue = true;
                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                           | SecurityProtocolType.Tls11
                           | SecurityProtocolType.Tls12
                           | SecurityProtocolType.Ssl3;

                    var request = (HttpWebRequest)WebRequest.Create(tokenEndpoint);
                    request.Method = "POST";

                    var postData = new Dictionary<string, string>
        {
            { "grant_type", grantType },
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "username", username },
            { "password", password }
        };

                    string requestBody = string.Join("&", postData
                        .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));

                    byte[] requestBodyBytes = Encoding.UTF8.GetBytes(requestBody);

                    request.ContentType = "application/x-www-form-urlencoded";
                    request.ContentLength = requestBodyBytes.Length;

                    using (var requestStream = request.GetRequestStream())
                    {
                        requestStream.Write(requestBodyBytes, 0, requestBodyBytes.Length);
                    }

                    try
                    {
                        using (var response = request.GetResponse())
                        using (var responseStream = response.GetResponseStream())
                        using (var reader = new StreamReader(responseStream))
                        {
                            string jsonResponse = reader.ReadToEnd();
                            var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonResponse);
                            token = tokenResponse.access_token;
                            HttpContext.Current.Session["token"] = token;
                            return token;

                        }
                    }
                    catch (WebException ex)
                    {
                        if (ex.Response is HttpWebResponse)
                        {
                            HttpWebResponse errorResponse = (HttpWebResponse)ex.Response;
                            using (var errorStream = errorResponse.GetResponseStream())
                            {
                                using (var reader = new StreamReader(errorStream))
                                {
                                    string errorResponseText = reader.ReadToEnd();
                                    throw new Exception($"Error: {errorResponseText}");
                                }
                            }
                        }

                        throw;
                    }

                }
            }
            else
            {
                return HttpContext.Current.Session["token"].ToString();
            }
        }



        private class TokenResponse
        {
            public string access_token { get; set; }
            public string instance_url { get; set; }
            public string id { get; set; }
            public string token_type { get; set; }
            public string issued_at { get; set; }
            public string signature { get; set; }
        }


        public string CreateLeadAndGetId(string firstname, string lastname, string phone, string email, string resedance = null, string gender = null, string nationality = null, string note = null, string edisname = null, string ediscode = null, string persontype = null, string uni = null, string program = null, string lang = null, string major = null, string passid = null, string birth = null, string father = null, string mother = null)
        {
            string url = "https://vaha.my.salesforce.com/services/data/v57.0/sobjects/Lead/";
            string accessToken = GetAccessToken();

            string requestBody = "{\r\n    \"FirstName\" : \"" + firstname + "\",\r\n    \"LastName\" : \"" + lastname + "\",\r\n    \"MobilePhone\" : \"" + phone + "\",\r\n    \"Email\" : \"" + email + "\",\r\n    \"OwnerId\" : \"0054L000001IJ70QAG\",\r\n    \"LeadSource\":\"Web\"";
            if (gender != null)
            {
                requestBody += ",\r\n    \"Gender__c\" : \"" + gender + "\"";
            }
            if (birth != null)
            {
                requestBody += ",\r\n    \"Date_of_Birth__c\" : \"" + birth + "\"";
            }
            if (mother != null)
            {
                requestBody += ",\r\n    \"Mother_s_Name__c\" : \"" + mother + "\"";
            }
            if (father != null)
            {
                requestBody += ",\r\n    \"Father_s_Name__c\" : \"" + father + "\"";
            }

            if (nationality != null)
            {
                requestBody += ",\r\n    \"Citizenship__c\" : \"" + nationality + "\"";
            }
            if (resedance != null)
            {
                requestBody += ",\r\n    \"Country_of_Residence__c\": \"" + resedance + "\"";
            }
            if (uni != null)
            {
                requestBody += ",\r\n    \"Preferred_University__c\": \"" + uni + "\"";
            }
            if (program != null)
            {
                requestBody += ",\r\n    \"Preferred_Degree__c\": \"" + program + "\"";
            }
            if (lang != null)
            {
                requestBody += ",\r\n    \"Native_Language__c\": \"" + lang + "\"";
            }
            if (major != null)
            {
                requestBody += ",\r\n    \"Preferred_Programs__c\": \"" + major + "\"";
            }
            if (passid != null)
            {
                requestBody += ",\r\n    \"Passport_Number__c\": \"" + passid + "\"";
            }
            if (note != null && note != "")
            {
                requestBody += ",\r\n    \"Notes__c\" : \"" + note + "\"";
            }
            if (edisname != null)
            {
                requestBody += ",\r\n    \"EDIS_Name__c\" : \"" + edisname + "\"";
            }
            if (ediscode != null)
            {
                requestBody += ",\r\n    \"EDIS_Code__c\" : \"" + ediscode + "\"";
            }
            if (persontype != null)
            {
                requestBody += ",\r\n    \"Person_Type_EDIS__c\" : \"" + persontype;
            }
            requestBody += "\r\n}";
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                   | SecurityProtocolType.Tls11
                   | SecurityProtocolType.Tls12
                   | SecurityProtocolType.Ssl3;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.Headers.Add("Authorization", "Bearer " + accessToken);
            request.ContentType = "application/json";
            request.Accept = "application/json";

            using (StreamWriter writer = new StreamWriter(request.GetRequestStream()))
            {
                writer.Write(requestBody);
            }

            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                    {
                        string responseContent = reader.ReadToEnd();
                        // تحليل الاستجابة للحصول على الـ Id
                        // يعتمد هذا على بنية الاستجابة المتوقعة
                        // يجب تعديلها بناءً على هيكل الاستجابة الفعلي
                        // القيمة المسترجعة هنا قيمة الـ Id المستخرجة
                        string leadId = ParseLeadIdFromResponse(responseContent);

                        return leadId;
                    }
                }
            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة
                insertevents("LP2023:", "", error, "website", System.DateTime.Now.ToString(), "0");
                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }
        }
        private string ParseLeadIdFromResponse(string response)
        {
            JObject responseJson = JObject.Parse(response);
            string leadId = (string)responseJson["id"];

            return leadId;
        }
        public void insertevents(string note, string refre, string events, string userevent, string time1, string color)
        {
            string connectionString = WebConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString;
            SqlConnection con = new SqlConnection(connectionString);
            con.Open();
            SqlCommand cmd4 = new SqlCommand("insert into events  (note,idstudent,events,userevents,time,color) Values (@note,@idstudent,@events,@userevents,@time,@color) ", con);

            cmd4.Parameters.AddWithValue("@events", events);
            cmd4.Parameters.AddWithValue("@userevents", userevent);
            cmd4.Parameters.AddWithValue("@note", note);
            cmd4.Parameters.AddWithValue("@color", color);
            cmd4.Parameters.AddWithValue("@idstudent", refre);
            cmd4.Parameters.AddWithValue("@time", time1);
            cmd4.ExecuteNonQuery();
            con.Close();
        }
    }
}