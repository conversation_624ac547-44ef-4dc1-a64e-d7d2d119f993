﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using unitededisprinter.service;
using ZXing;
using ZXing.Common;
using Zebra.Sdk.Printer.Discovery;
using Zebra.Sdk.Comm;
using Zebra.Sdk.Printer;
using ZXing.Rendering;
using static unitededisprinter.service.getuni;
using System.Xml.Linq;

namespace unitededisprinter.Controllers
{
    public class HomeController : Controller
    {
        // GET: cards
        public class Attributes
        {
            public string type { get; set; }
            public string url { get; set; }
        }
        public class Record
        {
            public string Id { get; set; }
        
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Email { get; set; }
            public string MobilePhone { get; set; }
    

        }
     
        public class marketing
        {
            public string Id { get; set; }

            public string Applicant_Name__c { get; set; }
          
            public string Applicant__c { get; set; }
            public string Applicant_Id__c { get; set; }
            public string utm_medium__c { get; set; }
            public string Name { get; set; }


        }
        public ActionResult Index()
        {
            getuni y = new getuni();
            return View();
        }
        public class RootObject
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public List<Record> records { get; set; }
        }
        getuni y = new getuni();
        static bool HasLetters(string content)
        {
            foreach (char c in content)
            {
                if (char.IsLetter(c))
                {
                    return true;
                }
            }
            return false;
        }
        static string CutFirst15Letters(string content)
        {
           
                string tt= content.Substring(0, 15);
                return tt;
          
        }
        async Task updateatt(string leadId, string United_Club_Member)
        {
            
            string sObject = "Marketing__c";

            //prepare the request body
            var body = new
            {
                Status__c = United_Club_Member
            };

            //update
            await y.Update(sObject, leadId, body);
        }
        async Task updatenooffr(string leadId, int United_Club_Member)
        {
          
            string sObject = "Marketing__c";

            //prepare the request body
            var body = new
            {
                Number_of_Attendee__c = United_Club_Member
            };

            //update
            await y.Update(sObject, leadId, body);
        }
        public async Task<ActionResult> GetPaggedData(int pageNumber, int pageSize, string lstate)
        {
            if (lstate != "")
            {
                try
                {

                    List<Record1> records = new List<Record1>();
                   string appid = "";
                    string leadid = lstate;
                    getuni t = new getuni();
                    //if (pageNumber > 1)
                    //{
                    //    appid = CutFirst15Letters(lstate);
                    //}
                    //else
                    //{

                    if (!HasLetters(lstate))
                    {
                    
                            records = await t.getliststudents1(lstate);

                    }
                    else
                    {
                        records = await t.getliststudents(lstate, pageNumber, pageSize);
                        await updateatt(lstate, "Attended");
                    }
                        if (records.Count <=0) { return Json("0", JsonRequestBehavior.AllowGet); }
                       

                    return Json(records, JsonRequestBehavior.AllowGet);
                }
                catch (Exception e)
                {
                    Console.WriteLine("حدث خطأ: " + e.Message);

                    throw;
                }
            }
            else
            {
                return Json("0", JsonRequestBehavior.AllowGet);
            }
        }
        //public async Task<ActionResult> GetPaggedData2(int pageNumber, int pageSize, string lstate)
        //{
           


        //        string appid = "";
        //        string leadid = lstate;
        //        getuni t = new getuni();

        //    appid = t.Getcontantid(CutFirst15Letters(lstate));


        //        var listData = await getliststudents(appid, pageNumber, pageSize);

        //        await updateatt(leadid, "Attended");



        //        return Json(listData, JsonRequestBehavior.AllowGet);
          
        //}
        static Bitmap GenerateQRCode(string qrData)
        {
            BarcodeWriter<Bitmap> barcodeWriter = new BarcodeWriter<Bitmap>
            {
                Format = BarcodeFormat.QR_CODE,
                Options = new EncodingOptions
                {
                    Width = 200, // عرض الصورة
                    Height = 200 // ارتفاع الصورة
                }
            };

            Bitmap qrCodeImage = barcodeWriter.Write(qrData);

            return qrCodeImage;
        }
        static string GetUSBPrinterName()
        {
            // يمكنك استخدام مكتبة مثل WMI للبحث عن الطابعات المتصلة عبر USB
            // هنا يتم افتراض أنه يتم اختيار الطابعة الافتراضية
            System.Drawing.Printing.PrintDocument printDoc = new System.Drawing.Printing.PrintDocument();
            return printDoc.PrinterSettings.PrinterName;
        }
        static byte[] ConvertImageToByteArray(Bitmap image)
        {
            using (var stream = new System.IO.MemoryStream())
            {
                image.Save(stream, ImageFormat.Png); // يمكنك استخدام BMP أو PNG حسب دعم الطابعة
                return stream.ToArray();
            }
        }

        public async Task<ActionResult> printy(string leadid, int count1)
        {
            string printerName = GetUSBPrinterName();

            if (!string.IsNullOrEmpty(printerName))
            {
                try
                {
                    foreach (DiscoveredPrinterDriver printer in UsbDiscoverer.GetZebraDriverPrinters())
                    {
                        Console.WriteLine(printer);
                    }

                    foreach (DiscoveredUsbPrinter usbPrinter in UsbDiscoverer.GetZebraUsbPrinters(new ZebraPrinterFilter()))
                    {
                        Console.WriteLine(usbPrinter);
                        printerName = usbPrinter.Address;
                    }
                }
                catch (ConnectionException e)
                {
                    Console.WriteLine($"Error discovering local printers: {e.Message}");
                    return Json($"Error discovering local printers: {e.Message}", JsonRequestBehavior.AllowGet);
                }

                Console.WriteLine("Done discovering local printers.");

                try
                {
                    await updatenooffr(leadid, count1);

                    if (string.IsNullOrWhiteSpace(printerName))
                    {
                        return Json("Printer name is missing or empty.", JsonRequestBehavior.AllowGet);
                    }

                    Connection connection = new UsbConnection(printerName);
                    try
                    {
                        connection.Open();
                    }
                    catch (Exception ex)
                    {
                        return Json("Could not open USB printer connection: " + ex.Message, JsonRequestBehavior.AllowGet);
                    }

                    string accid = y.Getaccountid(leadid);
                    ZebraPrinter printer1 = ZebraPrinterFactory.GetInstance(connection);
                    string marketingno = y.GetMarketing(leadid);
                    string qrData = leadid; // ✅ QR data source (lead ID)
                    string languageCode = y.Getlangid(accid);

                    int badgeWidth = 480;
                    int badgeHeight = 400;

                    BarcodeWriter<Bitmap> barcodeWriter = new BarcodeWriter<Bitmap>
                    {
                        Format = BarcodeFormat.QR_CODE,
                        Options = new EncodingOptions
                        {
                            Width = 280,
                            Height = 280,
                            Margin = 0
                        },
                        Renderer = new BitmapRenderer()
                    };

                    // ✅ Fixed variable name (qrData instead of qrCodeData)
                    Bitmap qrCodeImage = barcodeWriter.Write(qrData);
                    byte[] imageData = ConvertImageToByteArray(qrCodeImage);

                    for (int i = 1; i <= count1; i++)
                    {
                        string path1 = "C:\\images\\";
                        if (!Directory.Exists(path1))
                        {
                            Directory.CreateDirectory(path1);
                        }

                        string appid = y.Getcontantid(leadid);
                        var fname = FirstName(appid);
                        var lastname = LastName(appid);

                        Bitmap badgeImage = new Bitmap(badgeWidth, badgeHeight);
                        using (Graphics g = Graphics.FromImage(badgeImage))
                        {
                            g.Clear(Color.White);
                        }

                        Image qrImage;
                        using (MemoryStream ms = new MemoryStream(imageData))
                        {
                            qrImage = Image.FromStream(ms);
                        }

                        using (Graphics graphics = Graphics.FromImage(badgeImage))
                        {
                            Font topFont = new Font("Arial", 22, FontStyle.Bold);
                            SizeF topTextSize = graphics.MeasureString(marketingno, topFont);
                            PointF topTextLocation = new PointF((badgeWidth - topTextSize.Width) / 2, 15);
                            graphics.DrawString(marketingno, topFont, Brushes.Black, topTextLocation);

                            int qrSize = 280;
                            int qrX = (badgeWidth / 2) - (qrSize / 2) - 25;
                            int qrY = 60;
                            graphics.DrawImage(qrImage, qrX, qrY, qrSize, qrSize);

                            // ✅ Larger language circle configuration
                            int circleRadius = 50; // Increased size
                            int circleX = qrX + qrSize + 20 + circleRadius; // Maintain 10px padding
                            int circleY = qrY + (qrSize / 2) - (circleRadius / 2); // Vertically centered

                            Point circleCenter = new Point(circleX, circleY);

                            Pen pen = new Pen(Brushes.Black, 3); // Thicker border
                            graphics.DrawEllipse(pen, circleCenter.X - circleRadius, circleCenter.Y - circleRadius, circleRadius * 2, circleRadius * 2);
                            graphics.FillEllipse(Brushes.White, circleCenter.X - circleRadius + 1, circleCenter.Y - circleRadius + 1, (circleRadius * 2) - 2, (circleRadius * 2) - 2);

                            Font languageFont = new Font("Arial", 24, FontStyle.Bold); // Larger font
                            SizeF codeSize = graphics.MeasureString(languageCode, languageFont);
                            PointF codeLocation = new PointF(circleCenter.X - codeSize.Width / 2, circleCenter.Y - codeSize.Height / 2);
                            graphics.DrawString(languageCode, languageFont, Brushes.Black, codeLocation);

                            string bottomText = i != 1 ? "Visitor" : $"{fname.Result} {lastname.Result}";
                            Font bottomFont = new Font("Arial", 22, FontStyle.Bold);
                            SizeF bottomTextSize = graphics.MeasureString(bottomText, bottomFont);
                            int textY = qrY + qrSize + 20;
                            int leftOffset = 20; // Adjust this value for desired offset
                            float xPosition = (badgeWidth - bottomTextSize.Width) / 2 - leftOffset;
                            PointF bottomTextLocation = new PointF(xPosition, textY);

                            graphics.DrawString(bottomText, bottomFont, Brushes.Black, bottomTextLocation);
                        }

                        string randomFileName = Guid.NewGuid().ToString().Substring(0, 4) + ".png";
                        string fullPath = Path.Combine(path1, randomFileName);
                        badgeImage.Save(fullPath, System.Drawing.Imaging.ImageFormat.Png);

                        printer1.PrintImage(fullPath, 133, 0);
                        System.IO.File.Delete(fullPath);
                    }

                    connection.Close();
                    return Json("D", JsonRequestBehavior.AllowGet);
                }
                catch (Exception e)
                {
                    Console.WriteLine("حدث خطأ أثناء الطباعة: " + e.Message);
                    return Json("حدث خطأ أثناء الطباعة: " + e.Message, JsonRequestBehavior.AllowGet);
                }
            }
            else
            {
                return Json("لم يتم العثور على طابعة متصلة عبر USB.", JsonRequestBehavior.AllowGet);
            }
        }

        async Task<string> getpin(string leadId)
        {

            //prepare the query
            string query = "SELECT PIN_Code__pc FROM Account WHERE (Old_Id__c='" + leadId + "' and Record_Type_Developer_Name__c='Lead') or Id='" + leadId + "'";

            //Query
            var result = await y.Query(query);

            JObject json = JObject.Parse(result);
            string value = (string)json["records"][0]["PIN_Code__pc"];

            return value;
        }
        async Task<string> FirstName(string leadId)
        {

            //prepare the query
            string query = "SELECT FirstName FROM Contact WHERE Id='" + leadId + "'";

            //Query
            var result = await y.Query(query);

            JObject json = JObject.Parse(result);
            string value = (string)json["records"][0]["FirstName"];

            return value;
        }
        async Task<string> Email(string leadId)
        {

            //prepare the query
            string query = "SELECT Email FROM Contact WHERE Id='" + leadId + "'";

            //Query
            var result = await y.Query(query);

            JObject json = JObject.Parse(result);
            string value = (string)json["records"][0]["Email"];

            return value;
        }
        async Task<string> MobilePhone(string leadId)
        {

            //prepare the query
            string query = "SELECT MobilePhone FROM Contact WHERE Id='" + leadId + "'";

            //Query
            var result = await y.Query(query);

            JObject json = JObject.Parse(result);
            string value = (string)json["records"][0]["MobilePhone"];

            return value;
        }

        async Task<string> LastName(string leadId)
        {

            //prepare the query
            string query = "SELECT LastName FROM Contact WHERE Id='" + leadId + "'";

            //Query
            var result = await y.Query(query);

            JObject json = JObject.Parse(result);
            string value = (string)json["records"][0]["LastName"];

            return value;
        }
    }


    
}