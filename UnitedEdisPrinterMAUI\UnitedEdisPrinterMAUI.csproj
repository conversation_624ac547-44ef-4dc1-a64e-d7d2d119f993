﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<!-- Target Windows only for desktop application -->
		<TargetFrameworks>net9.0-windows10.0.19041.0</TargetFrameworks>
		<OutputType>WinExe</OutputType>
		<RootNamespace>UnitedEdisPrinterMAUI</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<UseWinUI>true</UseWinUI>

		<!-- Display name -->
		<ApplicationTitle>United EDIS Printer</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.unitededis.printer</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- Windows Desktop Configuration -->
		<WindowsPackageType>None</WindowsPackageType>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />

		<!-- Configuration files -->
		<MauiAsset Include="appsettings.json" LogicalName="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />

		<!-- Core dependencies from original project -->
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />

		<!-- QR Code and Image Processing - Simplified for now -->
		<PackageReference Include="System.Drawing.Common" Version="9.0.0" />

		<!-- Note: Zebra SDK packages removed due to .NET 8 compatibility issues -->
		<!-- Will implement alternative printer communication -->

		<!-- Additional utilities -->
		<PackageReference Include="FluentFTP" Version="48.0.2" />
		<PackageReference Include="Csv" Version="2.0.93" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
	</ItemGroup>

</Project>
