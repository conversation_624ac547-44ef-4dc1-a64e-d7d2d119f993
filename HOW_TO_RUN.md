# How to Run United EDIS Printer Application

## Quick Start - Running the Executable

### Option 1: Direct Execution (Recommended)
The application has been pre-built and is ready to run:

1. **Navigate to the publish folder:**
   ```
   cd UnitedEdisPrinterMAUI\publish
   ```

2. **Run the executable:**
   ```
   UnitedEdisPrinterMAUI.exe
   ```

### Option 2: Using Batch File
Double-click `RunUnitedEdisPrinter.bat` in the root directory.

### Option 3: Using PowerShell Script
Right-click `RunUnitedEdisPrinter.ps1` and select "Run with PowerShell".

## Development - Running from Source

### Prerequisites
- .NET 8.0 SDK
- MAUI workload installed: `dotnet workload install maui`

### Option 1: Using .NET CLI
```bash
# Navigate to project directory
cd UnitedEdisPrinterMAUI

# Run the application
dotnet run --framework net8.0-windows10.0.19041.0
```

### Option 2: Using Visual Studio
1. Open `UnitedEdisPrinterSolution.sln` in Visual Studio 2022
2. Set `UnitedEdisPrinterMAUI` as startup project
3. Press F5 or click "Start Debugging"

### Option 3: Using Solution File
```bash
# Build the entire solution
dotnet build UnitedEdisPrinterSolution.sln

# Run the MAUI project
dotnet run --project UnitedEdisPrinterMAUI --framework net8.0-windows10.0.19041.0
```

## Building and Publishing

### Build for Development
```bash
dotnet build UnitedEdisPrinterMAUI/UnitedEdisPrinterMAUI.csproj
```

### Publish for Distribution
```bash
cd UnitedEdisPrinterMAUI
dotnet publish -c Release -f net8.0-windows10.0.19041.0 --self-contained true -r win-x64 -o publish
```

This creates a self-contained executable in the `publish` folder that can run on any Windows machine without requiring .NET to be installed.

## Configuration

### Database Connection Strings
Before running, update `appsettings.json` in the MAUI project with your actual database connection strings:

```json
{
  "ConnectionStrings": {
    "DefaultConnectionclub": "Your connection string here",
    "unitedclub2023": "Your connection string here",
    "sitConnectionString": "Your connection string here",
    "sitedisConnectionString": "Your connection string here",
    "Model7": "Your connection string here"
  }
}
```

### Salesforce Configuration
The application retrieves Salesforce credentials from the database. Ensure your database has the proper entries in the `emails` table with type `tokeng`.

## Troubleshooting

### Application Won't Start
1. **Check .NET Runtime**: Ensure .NET 8.0 runtime is installed (not needed for self-contained deployment)
2. **Check Windows Version**: Requires Windows 10 version 1903 or later
3. **Check Dependencies**: All dependencies are included in the publish folder

### Database Connection Issues
1. **Verify Connection Strings**: Check `appsettings.json` for correct database connections
2. **Network Access**: Ensure the application can reach your database servers
3. **Credentials**: Verify database credentials are correct

### Printer Issues
1. **Default Printer**: Ensure a printer is set as default in Windows
2. **Printer Drivers**: Install proper printer drivers
3. **USB Connection**: For Zebra printers, ensure USB connection is working

### Salesforce Integration Issues
1. **Internet Connection**: Verify internet connectivity
2. **Credentials**: Check Salesforce credentials in the database
3. **API Access**: Ensure Salesforce API access is enabled

## File Structure

```
unitededisprinteryurdisi/
├── UnitedEdisPrinterSolution.sln          # Visual Studio solution file
├── RunUnitedEdisPrinter.bat               # Batch file launcher
├── RunUnitedEdisPrinter.ps1               # PowerShell launcher
├── HOW_TO_RUN.md                          # This file
├── UnitedEdisPrinterMAUI/                 # MAUI project folder
│   ├── UnitedEdisPrinterMAUI.csproj       # Project file
│   ├── appsettings.json                   # Configuration
│   ├── README.md                          # Project documentation
│   ├── publish/                           # Published executable
│   │   └── UnitedEdisPrinterMAUI.exe      # Main executable
│   ├── Models/                            # Data models
│   ├── Services/                          # Business logic
│   ├── ViewModels/                        # MVVM view models
│   └── Resources/                         # Images and assets
└── unitededisprinter/                     # Original ASP.NET MVC project (reference)
```

## Features Available

- ✅ QR Code/Mobile number input with calculator keypad
- ✅ Salesforce integration for attendee lookup
- ✅ Badge generation with QR codes
- ✅ Multi-attendee support (1-9 badges)
- ✅ Windows printer integration
- ✅ Language code display on badges
- ✅ Error handling and user feedback

## Performance Notes

- **Startup Time**: First launch may take 5-10 seconds
- **Memory Usage**: Approximately 100-200 MB RAM
- **Disk Space**: Published app requires ~500 MB
- **Network**: Only required for Salesforce API calls

## Support

For issues or questions:
1. Check this documentation first
2. Review the application logs
3. Verify configuration settings
4. Test with the original ASP.NET MVC version for comparison
