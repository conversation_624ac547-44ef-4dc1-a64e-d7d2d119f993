using System.Security.Cryptography;
using System.Text;
using System.Net;
using Newtonsoft.Json;
using System.Data.SqlClient;
using Newtonsoft.Json.Linq;
using UnitedEdisPrinterMAUI.Models;

namespace UnitedEdisPrinterMAUI.Services;

public class RsaHelper
{
    private static readonly string Key = "HwUnitedGroup@20";
    private readonly ConfigurationService _configService;
    private string? _cachedToken;
    private DateTime _tokenExpiry = DateTime.MinValue;

    public RsaHelper(ConfigurationService configService)
    {
        _configService = configService;
    }

    public string Encrypt2023(string plainText)
    {
        byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
        byte[] ivBytes;

        using (Aes aes = Aes.Create())
        {
            aes.Key = keyBytes;
            aes.GenerateIV();
            ivBytes = aes.IV;

            ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

            byte[] encryptedBytes;

            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                {
                    byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                    cs.Write(plainBytes, 0, plainBytes.Length);
                }
                encryptedBytes = ms.ToArray();
            }

            byte[] combinedBytes = new byte[ivBytes.Length + encryptedBytes.Length];
            Buffer.BlockCopy(ivBytes, 0, combinedBytes, 0, ivBytes.Length);
            Buffer.BlockCopy(encryptedBytes, 0, combinedBytes, ivBytes.Length, encryptedBytes.Length);

            return Convert.ToBase64String(combinedBytes);
        }
    }

    public string Decrypt2023(string encryptedText)
    {
        byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
        byte[] combinedBytes = Convert.FromBase64String(encryptedText);

        byte[] ivBytes = new byte[16];
        byte[] encryptedBytes = new byte[combinedBytes.Length - ivBytes.Length];

        Buffer.BlockCopy(combinedBytes, 0, ivBytes, 0, ivBytes.Length);
        Buffer.BlockCopy(combinedBytes, ivBytes.Length, encryptedBytes, 0, encryptedBytes.Length);

        using (Aes aes = Aes.Create())
        {
            aes.Key = keyBytes;
            aes.IV = ivBytes;
            aes.Padding = PaddingMode.PKCS7;

            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

            using (var ms = new MemoryStream(encryptedBytes))
            using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
            using (var msDecrypt = new MemoryStream())
            {
                int read;
                byte[] buffer = new byte[1024];

                while ((read = cs.Read(buffer, 0, buffer.Length)) > 0)
                {
                    msDecrypt.Write(buffer, 0, read);
                }

                return Encoding.UTF8.GetString(msDecrypt.ToArray());
            }
        }
    }

    public async Task<string> GetAccessTokenAsync()
    {
        // Check if we have a valid cached token
        if (!string.IsNullOrEmpty(_cachedToken) && DateTime.Now < _tokenExpiry)
        {
            return _cachedToken;
        }

        string tokenEndpoint = "https://login.salesforce.com/services/oauth2/token";
        string grantType = "password";
        string clientId = "cid";
        string clientSecret = "sid";
        string username = "<EMAIL>";
        string password = "yhyasoft";
        
        DateTime now = DateTime.Now;
        var connectionString = _configService.GetConnectionString("sitConnectionString");

        using var con = new SqlConnection(connectionString);
        await con.OpenAsync();

        // Get credentials from database
        var cmd6 = new SqlCommand("SELECT email FROM emails WHERE type=@type", con);
        var cmd5 = new SqlCommand("SELECT password FROM emails WHERE type=@type", con);
        var cmd4 = new SqlCommand("SELECT subject FROM emails WHERE type=@type", con);
        var cmd7 = new SqlCommand("SELECT subsubject FROM emails WHERE type=@type", con);
        var cmd2 = new SqlCommand("SELECT mface FROM emails WHERE type=@type", con);

        cmd6.Parameters.AddWithValue("@type", "tokeng");
        cmd5.Parameters.AddWithValue("@type", "tokeng");
        cmd4.Parameters.AddWithValue("@type", "tokeng");
        cmd7.Parameters.AddWithValue("@type", "tokeng");
        cmd2.Parameters.AddWithValue("@type", "tokeng");

        username = Convert.ToString(await cmd6.ExecuteScalarAsync()) ?? username;
        password = Decrypt2023(Convert.ToString(await cmd5.ExecuteScalarAsync()) ?? "") + 
                  Decrypt2023(Convert.ToString(await cmd2.ExecuteScalarAsync()) ?? "");
        clientId = Convert.ToString(await cmd4.ExecuteScalarAsync()) ?? clientId;
        clientSecret = Decrypt2023(Convert.ToString(await cmd7.ExecuteScalarAsync()) ?? "");

        return await RequestNewTokenAsync(tokenEndpoint, grantType, clientId, clientSecret, username, password);
    }

    private async Task<string> RequestNewTokenAsync(string tokenEndpoint, string grantType, 
        string clientId, string clientSecret, string username, string password)
    {
        using var client = new HttpClient();
        
        var postData = new Dictionary<string, string>
        {
            { "grant_type", grantType },
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "username", username },
            { "password", password }
        };

        var content = new FormUrlEncodedContent(postData);
        
        try
        {
            var response = await client.PostAsync(tokenEndpoint, content);
            var jsonResponse = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonResponse);
                _cachedToken = tokenResponse?.access_token ?? throw new Exception("Invalid token response");
                _tokenExpiry = DateTime.Now.AddHours(23); // Cache for 23 hours
                return _cachedToken;
            }
            else
            {
                throw new Exception($"Token request failed: {jsonResponse}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Error getting access token: {ex.Message}", ex);
        }
    }

    private class TokenResponse
    {
        public string? access_token { get; set; }
        public string? instance_url { get; set; }
        public string? id { get; set; }
        public string? token_type { get; set; }
        public string? issued_at { get; set; }
        public string? signature { get; set; }
    }

    public async Task InsertEventsAsync(string note, string refre, string events, string userevent, string time1, string color)
    {
        var connectionString = _configService.GetConnectionString("sitConnectionString");
        using var con = new SqlConnection(connectionString);
        await con.OpenAsync();
        
        var cmd = new SqlCommand("INSERT INTO events (note,idstudent,events,userevents,time,color) VALUES (@note,@idstudent,@events,@userevents,@time,@color)", con);
        
        cmd.Parameters.AddWithValue("@events", events);
        cmd.Parameters.AddWithValue("@userevents", userevent);
        cmd.Parameters.AddWithValue("@note", note);
        cmd.Parameters.AddWithValue("@color", color);
        cmd.Parameters.AddWithValue("@idstudent", refre);
        cmd.Parameters.AddWithValue("@time", time1);
        
        await cmd.ExecuteNonQueryAsync();
    }
}
