﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace unitededisprinter.Models
{
    public class setting
    {
        [Key]
        public int settingid { get; set; }
     
        public string key { get; set; }
        public string value { get; set; }
        public string client_id { get; set; }
        public string client_secret { get; set; }
        public string username { get; set; }
        public string password { get; set; }
        public string mpassword { get; set; }
        public DateTime last { get; set; } = DateTime.Now;
      

    }
}