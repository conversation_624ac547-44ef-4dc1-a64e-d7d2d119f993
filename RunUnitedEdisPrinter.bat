@echo off
echo Starting United EDIS Printer Application...
echo.

REM Check if the working executable exists
if exist "UnitedEdisPrinterMAUI\publish-working\UnitedEdisPrinterMAUI.exe" (
    echo Launching United EDIS Printer from working build...
    start "" "UnitedEdisPrinterMAUI\publish-working\UnitedEdisPrinterMAUI.exe"
    goto :success
)

REM Check if the original executable exists
if exist "UnitedEdisPrinterMAUI\publish\UnitedEdisPrinterMAUI.exe" (
    echo Launching United EDIS Printer from original build...
    start "" "UnitedEdisPrinterMAUI\publish\UnitedEdisPrinterMAUI.exe"
    goto :success
)

echo ERROR: UnitedEdisPrinterMAUI.exe not found!
echo Please ensure the application has been published.
echo Run: dotnet publish -c Release -f net9.0-windows10.0.19041.0 --self-contained true -r win-x64 -o publish-working
pause
exit /b 1

:success

echo Application started successfully!
echo You can close this window.
pause
