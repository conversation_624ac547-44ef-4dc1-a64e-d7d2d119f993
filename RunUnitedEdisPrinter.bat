@echo off
echo Starting United EDIS Printer Application...
echo.

REM Check if the executable exists
if not exist "UnitedEdisPrinterMAUI\publish\UnitedEdisPrinterMAUI.exe" (
    echo ERROR: UnitedEdisPrinterMAUI.exe not found!
    echo Please ensure the application has been published.
    echo Run: dotnet publish -c Release -f net8.0-windows10.0.19041.0 --self-contained true -r win-x64 -o publish
    pause
    exit /b 1
)

REM Start the application
echo Launching United EDIS Printer...
start "" "UnitedEdisPrinterMAUI\publish\UnitedEdisPrinterMAUI.exe"

echo Application started successfully!
echo You can close this window.
pause
