using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace UnitedEdisPrinterMAUI.ViewModels;

public partial class SimpleTestViewModel : ObservableObject
{
    [ObservableProperty]
    private string qrOrMobileInput = string.Empty;

    [ObservableProperty]
    private bool isSearching = false;

    [ObservableProperty]
    private bool isShowingResults = false;

    [ObservableProperty]
    private bool isShowingAttendeeSelection = false;

    [ObservableProperty]
    private bool isPrinting = false;

    [ObservableProperty]
    private string selectedLeadId = string.Empty;

    [ObservableProperty]
    private string selectedApplicantName = string.Empty;

    [ObservableProperty]
    private string selectedMobile = string.Empty;

    [ObservableProperty]
    private string statusMessage = "Application loaded successfully! Enter QR Code or Mobile Number.";

    public ObservableCollection<object> SearchResults { get; } = new();

    [RelayCommand]
    private async Task SearchAsync()
    {
        if (string.IsNullOrWhiteSpace(QrOrMobileInput))
        {
            StatusMessage = "Please enter QR Code or Mobile Number";
            return;
        }

        IsSearching = true;
        StatusMessage = "Searching... (Demo Mode)";

        // Simulate search delay
        await Task.Delay(2000);

        StatusMessage = $"Demo: Would search for '{QrOrMobileInput}' in Salesforce";
        IsSearching = false;
    }

    [RelayCommand]
    private async Task PrintBadgesAsync(int attendeeCount)
    {
        IsPrinting = true;
        StatusMessage = $"Demo: Would print {attendeeCount} badges";

        // Simulate printing delay
        await Task.Delay(3000);

        StatusMessage = "Demo: Badges printed successfully!";
        IsPrinting = false;

        await Task.Delay(2000);
        ResetToHome();
    }

    [RelayCommand]
    private void AddDigit(string digit)
    {
        if (digit == "Reset")
        {
            QrOrMobileInput = string.Empty;
        }
        else if (digit == "Backspace")
        {
            if (QrOrMobileInput.Length > 0)
            {
                QrOrMobileInput = QrOrMobileInput[..^1];
            }
        }
        else
        {
            QrOrMobileInput += digit;
        }
    }

    [RelayCommand]
    private void BackToHome()
    {
        ResetToHome();
    }

    private void ResetToHome()
    {
        QrOrMobileInput = string.Empty;
        SelectedLeadId = string.Empty;
        SelectedApplicantName = string.Empty;
        SelectedMobile = string.Empty;
        StatusMessage = "Application ready! Enter QR Code or Mobile Number.";
        IsShowingResults = false;
        IsShowingAttendeeSelection = false;
        SearchResults.Clear();
    }
}
