namespace unitededisprinter.Models
{
    using System;
    using System.Data.Entity;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using unitededisprinter.Models;


    public partial class Model7 : DbContext
    {
        public Model7()
            : base("name=Model7")
        {
        }
       
       
        public virtual DbSet<setting>  Settings { get; set; }
       
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
        }
    }
}
