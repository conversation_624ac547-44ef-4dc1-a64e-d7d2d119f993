﻿using unitededisprinter.service;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using static unitededisprinter.Controllers.HomeController;
using System.Drawing.Printing;


namespace unitededisprinter.service
{
    public class getuni
    {
        public class QRCodeViewModel
        {

            public string Link { get; set; }


            public int Width { get; set; }
            public int Height { get; set; }
            public string QRCodeSize { get; set; }

            public HttpPostedFileBase Icon { get; set; }


            public int IconSize { get; set; }


            public int IconBorderWidth { get; set; }

            public string QRCodeImageBase64 { get; set; }
        }
        //private landingpage db = new landingpage();
        //private sit db1 = new sit();
        //private edis db11 = new edis();

        public int qrscan(string code)


        {


            string stmt = "";


            stmt = "SELECT COUNT(*) FROM unitedevents10 where year1='4'  and pcode='" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitedisConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public class Attributes1
        {
            public string type { get; set; }
            public string url { get; set; }
        }

        public class Record1
        {
            public Attributes1 attributes { get; set; }
            public string Applicant__c { get; set; }
            public string Applicant_Name__c { get; set; }
            public string Mobile__c { get; set; }
            public string Id { get; set; }
        }
        public class Root2
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public List<marketing> records { get; set; }
        }
        public class Root1
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public List<Record1> records { get; set; }
        }
        public string Getaccountid(string leadId)
        {

            //prepare the query
            string query = "SELECT Applicant_Id__c   FROM  Marketing__c WHERE Id = '" + leadId + "' AND (Event__c='a1AP2000001VR8DMAW'%20Or%20Event__c='a1AP2000001VS13MAG'%20Or%20Event__c='a1AP2000001VS4HMAW'%20Or%20Event__c='a1AP2000001VS5tMAG')";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            int totalSize = (int)json["totalSize"];
            if (totalSize > 0)
            {
                string value = (string)json["records"][0]["Applicant_Id__c"];
                if (value == null) { return "0"; }
                return value;
            }
            else { return "0"; }
        }
        public async Task<List<Record1>> getliststudents1(string leadId)
        {
            if (leadId.StartsWith("0"))
            {
                leadId = leadId.Substring(1);
            }

            string query = "SELECT Applicant__c,Applicant_Name__c,Id,Mobile__c   FROM Marketing__c WHERE%20Mobile__c%20LIKE%20'%25" + leadId + "%25'%20AND%20(Event__c='a1AP2000001VR8DMAW'%20Or%20Event__c='a1AP2000001VS13MAG'%20Or%20Event__c='a1AP2000001VS4HMAW'%20Or%20Event__c='a1AP2000001VS5tMAG')";




            //Query
            var result = await Query(query);

            List<Record1> records = JsonConvert.DeserializeObject<Root1>(result).records;



            return records;
        }
        public async Task<List<Record1>> getliststudents(string leadId, int pageno, int pagesize)
        {


            string query = "SELECT Applicant__c,Applicant_Name__c,Id,Mobile__c   FROM Marketing__c WHERE Id='" + leadId + "'   ";


            //Query
            var result = await Query(query);

            List<Record1> records = JsonConvert.DeserializeObject<Root1>(result).records;

            return records;
        }
        public string Getcontantidbymob(string leadId)
        {
            if (leadId.StartsWith("0"))
            {
                leadId = leadId.Substring(1);
            }

            //prepare the query
            string query = "SELECT Applicant__c,Mobile__c,Applicant_Name__c   FROM Marketing__c WHERE%20Mobile__c%20LIKE%20'%25" + leadId + "%25'%20AND%20(Event__c='a1AP2000001VR8DMAW'%20Or%20Event__c='a1AP2000001VS13MAG'%20Or%20Event__c='a1AP2000001VS4HMAW'%20Or%20Event__c='a1AP2000001VS5tMAG')";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            int totalSize = (int)json["totalSize"];
            if (totalSize >= 1)
            {
                return "1";
            }
            else if (totalSize > 0)
            {
                string value = (string)json["records"][0]["Applicant__c"];

                return value;
            }
            else
            {
                return "0";
            }
        }
        public string Getcontantid(string leadId)
        {

            //prepare the query
            string query = "SELECT Applicant__c   FROM  Marketing__c WHERE Id = '" + leadId + "' AND (Event__c='a1AP2000001VR8DMAW'%20Or%20Event__c='a1AP2000001VS13MAG'%20Or%20Event__c='a1AP2000001VS4HMAW'%20Or%20Event__c='a1AP2000001VS5tMAG')";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            int totalSize = (int)json["totalSize"];
            if (totalSize > 0)
            {
                string value = (string)json["records"][0]["Applicant__c"];
                if (value == null) { return "0"; }
                return value;
            }
            else { return "0"; }
        }
        public string Getlangid(string leadId)
        {

            //prepare the query
            string query = "SELECT utm_medium__pc FROM Account WHERE Id = '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            int totalSize = (int)json["totalSize"];
            if (totalSize > 0)
            {
                string value = (string)json["records"][0]["utm_medium__pc"];
                if (value == null) { return "0"; }
                return value.Replace("_landing_page", "");
            }
            else { return "0"; }
        }
        public async Task<List<marketing>> getinfo(string leadId)
        {
            //prepare the query
            string query = "SELECT Applicant_Id__c,Name,Id,utm_medium__c,Applicant__c,Applicant_Name__c FROM Marketing__c WHERE Id = '" + leadId + "' AND (Event__c='a1AP2000001VR8DMAW'%20Or%20Event__c='a1AP2000001VS13MAG'%20Or%20Event__c='a1AP2000001VS4HMAW'%20Or%20Event__c='a1AP2000001VS5tMAG')";

            //Query
            var result = await Query(query);

            List<marketing> records = JsonConvert.DeserializeObject<Root2>(result).records;

            return records;
        }
        public string GetMarketing(string leadId)
        {

            //prepare the query
            string query = "SELECT Name   FROM Marketing__c WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Name"];

            return value;
        }
        public string GetMarketingid(string leadId)
        {

            //prepare the query
            string query = "SELECT Id   FROM Marketing__c WHERE  Applicant__c= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Id"];

            return value;
        }

        public int totalnumber(string code)


        {


            string stmt = "";


            stmt = "SELECT COUNT(*) FROM stdirsms where  year='" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public int totalnumber2(string code)


        {


            string stmt = "";


            stmt = "SELECT COUNT(*) FROM stdirsms where flag IS NOT NULL and   year='" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public string smsname(string code)


        {
            string scode = "";
            string ccmd = "";
            //
            ccmd = " SELECT TOP(1) ads1 FROM [stdirsms] WHERE  year='" + code + "'  ";
            string connectionString = ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString;
            SqlConnection connection = new SqlConnection(connectionString);

            using (connection)
            {
                SqlCommand command = new SqlCommand(ccmd
                      ,
                connection);
                connection.Open();

                SqlDataReader reader = command.ExecuteReader();

                if (reader.HasRows)
                {
                    while (reader.Read())
                    {

                        scode = reader.IsDBNull(0) ? string.Empty : reader.GetValue(0).ToString();



                    }
                }
                else
                {

                }
                reader.Close();
                connection.Close();

            }
            return scode;
        }
        public string smsdate(string code)


        {
            string scode = "";
            string ccmd = "";
            //
            ccmd = " SELECT TOP(1) notedate FROM [stdirsms] WHERE  year='" + code + "'  ";
            string connectionString = ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString;
            SqlConnection connection = new SqlConnection(connectionString);

            using (connection)
            {
                SqlCommand command = new SqlCommand(ccmd
                      ,
                connection);
                connection.Open();

                SqlDataReader reader = command.ExecuteReader();

                if (reader.HasRows)
                {
                    while (reader.Read())
                    {

                        scode = reader.IsDBNull(0) ? string.Empty : reader.GetValue(0).ToString();



                    }
                }
                else
                {

                }
                reader.Close();
                connection.Close();

            }
            return scode;
        }
        public int totalnumber3(string code)


        {


            string stmt = "";


            stmt = "SELECT COUNT(*) FROM stdirsms where impemp1='1' and  year='" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public class edislist
        {
            public string name { get; set; }
            public string phone { get; set; }
            public string email { get; set; }
            public string leadstate { get; set; }
            public string leadstatecolor { get; set; }
            public string location { get; set; }
            public string id { get; set; }
        }
        public class statiks
        {
            public string noregist { get; set; }
            public string nouni { get; set; }
            public string nofirstpay { get; set; }
            public string lastpay { get; set; }

        }

        public int edis(string code)


        {
            string logo115;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);

            SqlCommand cmd44 = new SqlCommand("SELECT year FROM yearcycle ", con1);
            using (con1)
            {
                con1.Open(); logo115 = Convert.ToString(cmd44.ExecuteScalar());
                con1.Close();

            }
            string stmt = "";


            stmt = "SELECT COUNT(*) FROM students where year='" + logo115 + "' and wherel='From EDIS' and anotheruni!=N'امتحان منحة جامعة اسطنبول ايدن' and refre=N'" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public int edisaydin(string code)


        {
            string logo115;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);

            SqlCommand cmd44 = new SqlCommand("SELECT year FROM yearcycle ", con1);
            using (con1)
            {
                con1.Open(); logo115 = Convert.ToString(cmd44.ExecuteScalar());
                con1.Close();

            }
            string stmt = "";


            stmt = "SELECT COUNT(*) FROM students where year='" + logo115 + "' and wherel='From EDIS' and anotheruni=N'امتحان منحة جامعة اسطنبول ايدن' and refre=N'" + code + "' ";









            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }


            return count;
        }
        public string rightnumber(string input)
        {
            string EnglishNumbers = input.Replace("(", "");
            EnglishNumbers = EnglishNumbers.Replace(")", "");
            EnglishNumbers = EnglishNumbers.Replace("-", "");
            EnglishNumbers = EnglishNumbers.Replace(" ", "");
            EnglishNumbers = EnglishNumbers.Replace("  ", "");
            EnglishNumbers = EnglishNumbers.Replace("+", "00");
            if (HasArabicCharacters(EnglishNumbers))
            {
                EnglishNumbers = toEnglishNumber(EnglishNumbers).Trim();
            }

            return EnglishNumbers;
        }
        public string toEnglishNumber(string input)
        {
            string EnglishNumbers = "";

            for (int i = 0; i < input.Length; i++)
            {
                if (Char.IsDigit(input[i]))
                {
                    EnglishNumbers += char.GetNumericValue(input, i);
                }
                else
                {
                    EnglishNumbers += input[i].ToString();
                }
            }
            return EnglishNumbers;
        }
        public bool HasArabicCharacters(string text)
        {
            string c = text.Substring(0, 1);
            var regex = new Regex("^[\u0621-\u064A\u0660-\u0669 ]+$");

            return regex.IsMatch(c);
        }

        public string countleds19(string emp, string year)
        {
            string stmt = "SELECT COUNT(*) FROM students where ( emp=N'" + emp + "')  and (belongto IS  NULL or note='-')";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();

                }
            }
            return count.ToString();

        }
        public string userid(string emp)
        {
            string stmt = "SELECT id FROM login WHERE users='" + emp + "'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();

                }
            }
            return count.ToString();

        }
        public string userbyid(string emp)
        {
            string stmt = "SELECT users FROM login WHERE id=" + emp + "";
            string count = "";

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (string)cmdCount.ExecuteScalar();

                }
            }
            return count;

        }

        public string totallead8(string emp)
        {
            emp = emp.Replace("_", ".");
            int year = timeshifts();
            var x2 = DateTime.Today.ToString("yyyy-MM-dd");
            int count = 0;
            string stmt = " SELECT count(*) FROM [events] where cast([date] as date) = '" + x2 + "'  and  events=N'updatestdir'  and  userevents=N'" + emp + "'";


            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }


            }

            return count.ToString();

        }
        public string totallead8edis(string emp)
        {
            emp = emp.Replace("_", ".");
            int year = timeshifts();
            var x2 = DateTime.Today.ToString("yyyy-MM-dd");
            int count = 0;
            string stmt = " SELECT count(*) FROM [events] where cast([date] as date) = '" + x2 + "'  and  events=N'updateunitedevents'  and  userevents=N'" + emp + "'";


            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }


            }

            return count.ToString();

        }
        public string checknumber(string code, string id)
        {
            var result = new string(code.Where(c => char.IsDigit(c)).ToArray());
            string idasp = "";
            if (code != "")
            {
                string connectionString = ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString;
                SqlConnection connection0 = new SqlConnection(connectionString);

                using (connection0)
                {
                    SqlCommand command = new SqlCommand(
                      "SELECT * FROM stdir where  mobile like '%" + result + "%' and id!=" + id + " ",
                      connection0);
                    connection0.Open();
                    object x1 = command.ExecuteScalar();
                    if (x1 != null) { idasp = "1"; } else { }
                    connection0.Close();
                }

                SqlConnection connection11 = new SqlConnection(connectionString);
                using (connection11)
                {
                    SqlCommand command = new SqlCommand(
                      "SELECT * FROM [students] where mobile like '%" + result + "%' and id!=" + id + " ",
                      connection11);
                    connection11.Open();

                    object x1 = command.ExecuteScalar();
                    if (x1 != null) { idasp = "1"; } else { }
                    connection11.Close();
                }
            }
            return idasp;
        }
        public string ledstate9(string code)
        {
            var idasp = "";
            if (code != null)
            {
                string cmd112 = "";
                SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
                cmd112 = "SELECT ledstate FROM ledstate WHERE ledstateid=@itemid";
                SqlCommand cmd888 = new SqlCommand(cmd112, con4);


                cmd888.Parameters.AddWithValue("@itemid", code);

                using (con4)
                {
                    con4.Open();
                    object x1 = cmd888.ExecuteScalar();
                    if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "-"; }

                    con4.Close();


                }
            }
            else { idasp = "-"; }

            return idasp;

        }

        public string location(string tt)
        {

            var idasp = "";
            string cmd112 = "";

            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitedisConnectionString"].ConnectionString);
            cmd112 = "SELECT location FROM location WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", tt);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }


            return idasp;
        }
        public int timeshifts()
        {
            int issub1 = 0;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }


            int idaspint = Convert.ToInt16(issub1);

            return idaspint;
        }
        public string gettrack(string code)
        {
            var idasp = "";
            string cmd112 = "";

            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT refre FROM students WHERE trackno=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); idasp = "done"; } else { idasp = "0"; }

                con4.Close();


            }


            return idasp;

        }
        public string arid13(string code)
        {
            var idasp = "";
            string cmd112 = "";
            if (code != "")
            {
                SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
                cmd112 = "SELECT uniid FROM majors WHERE majors LIKE '%' + @itemid + '%'";
                SqlCommand cmd888 = new SqlCommand(cmd112, con4);


                cmd888.Parameters.AddWithValue("@itemid", code);

                using (con4)
                {
                    con4.Open();
                    object x1 = cmd888.ExecuteScalar();
                    if (x1 != null) { idasp = Convert.ToString(x1); idasp = "s122"; } else { idasp = "0"; }

                    con4.Close();


                }
            }
            else { idasp = "s122"; }
            return idasp;

        }
        public string title(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT title FROM seos WHERE seoid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string keywords(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT keywords FROM seos WHERE seoid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string description(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT des FROM seos WHERE seoid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string photo(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT photofulllink FROM seos WHERE seoid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string level(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT level FROM level WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string levelen(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT level FROM levelen WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string unicode(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT unicode FROM unidayna WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string unicodeen(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT unicode FROM unidaynaen WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string arid(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT Id FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle12(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uninamel FROM unidayna WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string uninamel(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uninamel FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string linklogo(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT linklogo FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string unilogo(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT linklogo FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string uniname(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uniname FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string city(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT city FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string constr(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT constr FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string visi(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT visi FROM unidayna WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string getdenklik(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM denklik where (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + ")  ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string gettranslate(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(DISTINCT refre) FROM studentstranslatefile where (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + ")  ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string getschstudents(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM schstudents where (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + ")  ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string getstudents(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM students where (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + ")  ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denstudentacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatedenst' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatedenst' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatedenst' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatedenst' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatedenst' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string translatestudentacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'تعديل ' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'تعديل ' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'تعديل ' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'تعديل ' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'تعديل ' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string translateaddfiles(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'اضافة ' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'اضافة ' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'اضافة ' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'اضافة ' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and note LIKE 'open file for studenttranslate student ID%' and events=N'اضافة ' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schstudentacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updateschst' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updateschst' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updateschst' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updateschst' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updateschst' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string studentacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatest' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatest' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatest' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatest' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and events='Updatest' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string studentfileacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatef'or events='تعديل' ) and idstudent LIKE 'f%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatef'or events='تعديل' ) and idstudent LIKE 'f%' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatef'or events='تعديل' ) and idstudent LIKE 'f%' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatef'or events='تعديل' ) and idstudent LIKE 'f%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatef'or events='تعديل' ) and idstudent LIKE 'f%' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schstudentfileacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updateschf'or events='تعديل' ) and idstudent LIKE 'schf%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updateschf'or events='تعديل' ) and idstudent LIKE 'schf%' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updateschf'or events='تعديل' ) and idstudent LIKE 'schf%' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updateschf'or events='تعديل' ) and idstudent LIKE 'schf%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updateschf'or events='تعديل' ) and idstudent LIKE 'schf%' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denstudentfileacts(string users, string type, string from, string to, string year1)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatedenf'or events='تعديل' ) and idstudent LIKE 'denf%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " AND DATEPART(dd, [date]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatedenf'or events='تعديل' ) and idstudent LIKE 'denf%' and (DATEPART(yy, [date]) =" + year + " )  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatedenf'or events='تعديل' ) and idstudent LIKE 'denf%' and [date] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatedenf'or events='تعديل' ) and idstudent LIKE 'denf%' and (DATEPART(yy, [date]) =" + year + " AND DATEPART(mm, [date]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM events where userevents='" + users + "' and (events='updatedenf'or events='تعديل' ) and idstudent LIKE 'denf%' and [date] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfiles(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and filecreator='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfiles(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and filecreator='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfiles(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and filecreator='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and filecreator='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilewith(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-'  and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and  filecreator='" + users + "'  and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and  filecreator='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and  filecreator='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and  filecreator='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilewitout(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfilewitout(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilewitout(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilewitoutagent(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilestateagent(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfilewitoutagent(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilewitoutagent(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilestateagent(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfilestateagent(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilewitoutagent2(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilestateagent2(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfilewitoutagent2(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilewitoutagent2(string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and  (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfilestateagent2(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilestateagent2(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string denaddfilestate(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfilestate(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilestate(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilestatewith(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfilestatewith(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilestatewith(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }


        public string addfileprepaywithuser(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfilestatewith13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string addfileprepaywithuser13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string denaddfileprepaywithuser(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfilestatewith13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string denaddfileprepaywithuser13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfileprepay(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string denaddfileprepayagent(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfileprepaywithuser(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfilestatewith13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and state='" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfileprepaywithuser13(string users, string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and ( agentid='Label' or  agentid is null)   and filecreator='" + users + "' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfileprepay(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfileprepayagent(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string addfileprepay(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and  fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string addfileprepayagent(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfileprepayagentunited(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and agentid='" + agent + "' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfileprepayagent2(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfileprepayagent2(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string addfileprepayagent2(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate like N'%" + state + "%'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfileprepay13(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string addfileprepayagent13(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string addfileprepayagent213(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfileprepay13(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schaddfileprepayagent13(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schaddfileprepayagent213(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfileprepay13(string type, string from, string to, string state, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and  fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string denaddfileprepayagent13(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where not(  agentid='Label') and not( agentid is null) and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string denaddfileprepayagent213(string type, string from, string to, string state, string year1, string agent)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " )  ";
            }
            else if (type == "6")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and year='" + year1 + "'   ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where agentid='1022' and isfile='-' and fstate=N'" + state + "'   and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string densendfile(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and flastmod='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }

        public string schsendfile(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and flastmod='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string sendfile(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " AND DATEPART(dd, [fdatec]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and flastmod='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and flastmod='" + users + "'  and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where isfile='-' and flastmod='" + users + "'  and [fdatec] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string followme(string users, string type, string from, string to, string year1)


        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            int month = x.Month;
            int gun = x.Day; string stmt = "";
            DateTime x2 = x.AddDays(-7);
            if (type == "1")
            {
                stmt = "SELECT COUNT(*) FROM students where folloto='" + users + "'  and (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + " AND DATEPART(dd, [today]) =" + gun + ")  ";
            }
            else if (type == "4")
            {
                stmt = "SELECT COUNT(*) FROM students where folloto='" + users + "'  and year='" + year1 + "'  ";
            }
            else if (type == "2")
            {
                stmt = "SELECT COUNT(*) FROM students where folloto='" + users + "'  and [today] between  '" + x2 + "' and '" + x + "'  ";
            }
            else if (type == "3")
            {
                stmt = "SELECT COUNT(*) FROM students where folloto='" + users + "'  and (DATEPART(yy, [today]) =" + year + " AND DATEPART(mm, [today]) =" + month + " )  ";
            }
            else
            {
                stmt = "SELECT COUNT(*) FROM students where folloto='" + users + "'  and [today] between  '" + from + "' and '" + to + "'  ";
            }




            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string dengetstudentfs(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM denklik where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + ")   ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string schgetstudentfs(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM schstudents where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + ")   ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string getstudentfs(string month)
        {
            string yahya = ""; Int16 issub1;
            SqlConnection con1 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            SqlCommand cmd7 = new SqlCommand("SELECT timeshift FROM timeshift", con1);



            using (con1)
            {
                con1.Open();

                issub1 = Convert.ToInt16(cmd7.ExecuteScalar());
                con1.Close();
            }
            DateTime x = DateTime.Now.AddHours(issub1);
            int year = x.Year;
            string stmt = "SELECT COUNT(*) FROM students where isfile='-' and (DATEPART(yy, [fdatec]) =" + year + " AND DATEPART(mm, [fdatec]) =" + month + ")   ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }

            yahya = count.ToString();
            return yahya;
        }
        public string yearcycle122(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uniname FROM unidayna WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle122en(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uniname FROM unidaynaen WHERE Id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string uninameen(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uniname FROM unidaynaen WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string uninameenl(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT uninamel FROM unidaynaen WHERE unicode=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle1990(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT fu5 FROM login WHERE users=@users";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@users", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { }

                con4.Close();


            }
            return idasp;

        }
        public string photolink(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT photolink FROM login WHERE users=@users";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@users", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { }

                con4.Close();


            }
            return idasp;

        }
        public string name(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT employeenameen FROM login WHERE users=@users";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@users", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { }

                con4.Close();


            }
            return idasp;

        }
        public string work(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT worken FROM login WHERE users=@users";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@users", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle999(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM students WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle9999(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM schstudents WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }

        public string yearcycle9999uc(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM ucorder WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle9999shome(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM shomeorder WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }

        public string yearcycle9999den(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM denklik WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle9999trans(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM studentstransfers WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle9999ika(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM studentsikamet WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle9999translate(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT year FROM studentstranslatefile WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string namest(int code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT stname FROM students WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            con4.Open();

            SqlDataReader reader = cmd888.ExecuteReader();

            if (reader.HasRows)
            {
                while (reader.Read())
                {
                    idasp = reader.GetString(0);


                }
            }

            reader.Close();
            con4.Close();

            return idasp;

        }
        public string nametranslate(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT stname FROM studentstranslatefile WHERE refre=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            con4.Open();

            SqlDataReader reader = cmd888.ExecuteReader();

            if (reader.HasRows)
            {
                while (reader.Read())
                {
                    idasp = reader.GetString(0);


                }
            }

            reader.Close();
            con4.Close();

            return idasp;

        }
        public string yearcycle124(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT isfile FROM students WHERE id=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle13(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString);
            cmd112 = "SELECT arid FROM unidaynaen WHERE uninamel=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle14(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitbConnectionString"].ConnectionString);
            cmd112 = "SELECT link FROM charin WHERE des=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearstudy()
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["tuedab"].ConnectionString);
            cmd112 = "SELECT studyyearvalue FROM studyyears WHERE studyyearid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", "1");

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string getagentname(string user)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["tuedab"].ConnectionString);
            cmd112 = "SELECT agentname FROM agents WHERE userid=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", user);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle15(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitbConnectionString"].ConnectionString);
            cmd112 = "SELECT Id FROM charin WHERE des=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public string yearcycle16(string code)
        {
            var idasp = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["sitbConnectionString"].ConnectionString);
            cmd112 = "SELECT desen FROM charin WHERE des=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", code);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { idasp = Convert.ToString(x1); } else { idasp = "0"; }

                con4.Close();


            }
            return idasp;

        }
        public int countemp()
        {

            string stmt = "SELECT COUNT(*) FROM login ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countuni()
        {


            string stmt = "SELECT COUNT(*) FROM unidayna ";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countinsttr()
        {


            string stmt = "SELECT COUNT(DISTINCT major) FROM institu where tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countinsten()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM institu where en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countinstsp()
        {


            string stmt = "SELECT COUNT(DISTINCT major) FROM institu where sp='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countinstru()
        {


            string stmt = "SELECT COUNT(DISTINCT major) FROM institu where ru='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachtr()
        {


            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachen()
        {


            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachsp()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where sp='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachru()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where ru='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachar()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where ar='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachge()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where ge='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbachch()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where ch='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbach30en70tr()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where [30en70tr]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countbach50en50ar()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM bachlor where [50en50ar]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbatrw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaenw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba30en70trw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and [30en70tr]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba50en50arw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and [50en50ar]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaruw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and ru='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaarw()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where  [with]='1' and ar='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbagew()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where [with]='1' and ge='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbatrwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaenwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba30en70trwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and [30en70tr]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba50en50arwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and [50en50ar]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaruwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and ru='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaarwout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and ar='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbagewout()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where without='1' and ge='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbatron()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaenon()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba30en70tron()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and [30en70tr]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmba50en50aron()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and [50en50ar]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaruon()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and ru='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbaaron()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and ar='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countmbageon()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM mba where online='1' and ge='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countphdtr()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM phd where tr='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countphden()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM phd where en='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countphd30en70tr()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM phd where [30en70tr]='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public int countphdar()
        {
            var idasp = "";

            string stmt = "SELECT COUNT(DISTINCT major) FROM phd where ar='1'";
            int count = 0;

            using (SqlConnection thisConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString))
            {
                using (SqlCommand cmdCount = new SqlCommand(stmt, thisConnection))
                {
                    thisConnection.Open();
                    count = (int)cmdCount.ExecuteScalar();
                    thisConnection.Close();
                }
            }
            return count;

        }
        public string userrole(string user)
        {

            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT roleid FROM login WHERE users=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", user);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }
            return yahya1;
        }
        public string uniper(string user, string type)
        {
            string yahya = "";
            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            cmd112 = "SELECT roleid FROM login WHERE users=@itemid";
            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", user);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            con.Open();
            SqlCommand cmd = new SqlCommand("select * from sitNetRolespages where roleid =@users and pageid=@password ", con);
            cmd.Parameters.AddWithValue("@users", yahya1);
            cmd.Parameters.AddWithValue("@password", type);
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            da.Fill(dt);
            if (dt.Rows.Count > 0)
            { yahya = "1"; }
            else { yahya = "0"; }
            return yahya;
        }
        public string filelink(string id, string type)
        {
            string yahya = "";
            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
            if (type == "inst") { cmd112 = "SELECT pdflink FROM institu WHERE uniid=@itemid"; }
            else if (type == "col") { cmd112 = "SELECT pdflink FROM bachlor WHERE uniid=@itemid"; }
            else if (type == "mba") { cmd112 = "SELECT pdflink FROM mba WHERE uniid=@itemid"; }
            else if (type == "phd") { cmd112 = "SELECT pdflink FROM phd WHERE uniid=@itemid"; }


            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", id);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }

            return yahya1;
        }
        public string major(string id, string type)
        {

            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
            if (type == "inst") { cmd112 = "SELECT major FROM institu WHERE uniid=@itemid"; }
            else if (type == "col") { cmd112 = "SELECT major FROM bachlor WHERE uniid=@itemid"; }
            else if (type == "mba") { cmd112 = "SELECT major FROM mba WHERE uniid=@itemid"; }
            else if (type == "phd") { cmd112 = "SELECT major FROM phd WHERE uniid=@itemid"; }


            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", id);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }

            return yahya1;
        }
        public string uniname(string id, string type)
        {

            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
            if (type == "inst") { cmd112 = "SELECT uniname FROM institu WHERE uniid=@itemid"; }
            else if (type == "col") { cmd112 = "SELECT uniname FROM bachlor WHERE uniid=@itemid"; }
            else if (type == "mba") { cmd112 = "SELECT uniname FROM mba WHERE uniid=@itemid"; }
            else if (type == "phd") { cmd112 = "SELECT uniname FROM phd WHERE uniid=@itemid"; }


            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", id);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }

            return yahya1;
        }
        public string col(string id, string type)
        {

            string yahya1 = "";
            string cmd112 = "";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
            if (type == "inst") { cmd112 = "SELECT col FROM institu WHERE uniid=@itemid"; }
            else if (type == "col") { cmd112 = "SELECT col FROM bachlor WHERE uniid=@itemid"; }
            else if (type == "mba") { cmd112 = "SELECT col FROM mba WHERE uniid=@itemid"; }
            else if (type == "phd") { cmd112 = "SELECT col FROM phd WHERE uniid=@itemid"; }


            SqlCommand cmd888 = new SqlCommand(cmd112, con4);


            cmd888.Parameters.AddWithValue("@itemid", id);

            using (con4)
            {
                con4.Open();
                object x1 = cmd888.ExecuteScalar();
                if (x1 != null) { yahya1 = Convert.ToString(x1); } else { yahya1 = "0"; }

                con4.Close();


            }

            return yahya1;
        }
        public bool chkuser(string user, string pass)
        {

            bool yahya1 = false;
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            con.Open();
            SqlCommand cmd = new SqlCommand("select * from login where Users =@users and Password=@password and activiate='1' ", con);
            cmd.Parameters.AddWithValue("@users", user);
            cmd.Parameters.AddWithValue("@password", pass);
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            da.Fill(dt);
            if (dt.Rows.Count > 0)
            {
                string cellValue = dt.Rows[0][18].ToString();

                if (cellValue == "1")
                {

                    SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                    con4.Open();
                    SqlCommand cmd2 = new SqlCommand("select * from iplist where IP =@users  ", con4);
                    cmd2.Parameters.AddWithValue("@users", System.Web.HttpContext.Current.Request.UserHostAddress);
                    SqlDataAdapter da2 = new SqlDataAdapter(cmd2);
                    DataTable dt2 = new DataTable();
                    da2.Fill(dt2);
                    if (dt2.Rows.Count > 0)
                    {
                        yahya1 = true;



                    }
                    else
                    {

                        yahya1 = false;
                    }

                }
                else { yahya1 = true; }

            }
            else { yahya1 = false; }

            return yahya1;
        }
        public string murid(string unicode)
        {
            string yahya1 = "0";
            SqlConnection con4 = new SqlConnection(ConfigurationManager.ConnectionStrings["situniConnectionString"].ConnectionString);
            con4.Open();
            SqlCommand cmd2 = new SqlCommand("select * from [murshed] where unicode=@users  ", con4);
            cmd2.Parameters.AddWithValue("@users", unicode);
            SqlDataAdapter da2 = new SqlDataAdapter(cmd2);
            DataTable dt2 = new DataTable();
            da2.Fill(dt2);
            if (dt2.Rows.Count > 0)
            { yahya1 = "1"; }
            return yahya1;

        }

        public string updatevalue(string lang, string value, string oldvalueljson)
        {
            if (oldvalueljson.Contains("#"))
            {
                StringBuilder sb = new StringBuilder(oldvalueljson);
                sb.Replace("{#ar#:#", "{\"ar\":\"");
                sb.Replace("#,#en#:#", "\",\"en\":\"");
                sb.Replace("#}", "\"}");
                sb.Replace("#", "\"");
                oldvalueljson = sb.ToString();
            }
            string value1 = FriendlyURLTitle(value);

            JObject obj = JObject.Parse(oldvalueljson);
            var val = obj;

            if (lang == "ar")
            {
                val["ar"] = value1;
            }
            else
            {
                val["en"] = value1;

            }
            string result = JsonConvert.SerializeObject(obj);
            return result;
        }

        public string getvalue(string lang, string oldvalueljson)
        {
            if (oldvalueljson.Contains("#"))
            {
                StringBuilder sb = new StringBuilder(oldvalueljson);
                sb.Replace("{#ar#:#", "{\"ar\":\"");
                sb.Replace("#,#en#:#", "\",\"en\":\"");
                sb.Replace("#}", "\"}");
                sb.Replace("\\#", "\'}");
                sb.Replace("color: \"", "color: #");
                oldvalueljson = sb.ToString();
            }

            JObject obj = JObject.Parse(oldvalueljson);
            var val = obj;
            string result = "";
            if (lang == "ar")
            {
                result = val["ar"].ToString();
            }
            else
            {
                result = val["en"].ToString();

            }
            string lang2 = "";
            if (lang == "ar") { lang2 = "en"; }
            else
            {
                lang2 = "ar";
            }
            string value = "";
            if (result != "")
            {
                value = result;
            }
            else { value = val[lang2].ToString(); }
            return value;

        }
        public string FriendlyURLTitle(string incomingText)
        {

            if (incomingText != null)
            {
                incomingText = incomingText.Replace("ş", "s");
                incomingText = incomingText.Replace("Ş", "s");
                incomingText = incomingText.Replace("İ", "i");
                incomingText = incomingText.Replace("I", "i");
                incomingText = incomingText.Replace("ı", "i");
                incomingText = incomingText.Replace("ö", "o");
                incomingText = incomingText.Replace("Ö", "o");
                incomingText = incomingText.Replace("ü", "u");
                incomingText = incomingText.Replace("Ü", "u");
                incomingText = incomingText.Replace("Ç", "c");
                incomingText = incomingText.Replace("ç", "c");
                incomingText = incomingText.Replace("ğ", "g");
                incomingText = incomingText.Replace("Ğ", "g");

                incomingText = incomingText.Replace("---", "-");
                incomingText = incomingText.Replace("?", "");
                incomingText = incomingText.Replace("/", "");
                incomingText = incomingText.Replace("\"", "");

                incomingText = incomingText.Replace("'", "");
                incomingText = incomingText.Replace("#", "");
                incomingText = incomingText.Replace("%", "");
                incomingText = incomingText.Replace("&", "");
                incomingText = incomingText.Replace("*", "");
                incomingText = incomingText.Replace("!", "");

                incomingText = incomingText.Replace("+", "");
                incomingText = incomingText.Replace("<", "");
                incomingText = incomingText.Replace(">", "");
                incomingText = incomingText.Replace("=", "");
                incomingText = incomingText.Replace("{", "");
                incomingText = incomingText.Replace("}", "");
                incomingText = incomingText.Replace(";", "");



                string encodedUrl = incomingText;
                // & ile " " yer değiştirme
                encodedUrl = Regex.Replace(encodedUrl, @"\&+", "and");
                // " " karakterlerini silme
                encodedUrl = encodedUrl.Replace("'", "");
                // geçersiz karakterleri sil

                // tekrar edenleri sil
                encodedUrl = Regex.Replace(encodedUrl, @"-+", "-");

                return encodedUrl;
            }
            else
            {
                return "";
            }
        }

        public async Task<string> Insert(string sobject, object body)
        {
            RsaHelper y = new RsaHelper();
            string salceidtoken = y.GetAccessToken();

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                   | SecurityProtocolType.Tls11
                   | SecurityProtocolType.Tls12
                   | SecurityProtocolType.Ssl3;
            var request1 = WebRequest.Create("https://vaha.my.salesforce.com/services/data/v57.0/sobjects/" + sobject);
            request1.Method = "POST";
            request1.Headers.Add("Authorization", "Bearer " + salceidtoken);
            request1.ContentType = "application/json";

            var json = JsonConvert.SerializeObject(body);
            var byteData = Encoding.UTF8.GetBytes(json);
            request1.ContentLength = byteData.Length;

            using (var stream1 = request1.GetRequestStream())
            {
                stream1.Write(byteData, 0, byteData.Length);
            }
            try
            {
                var response1 = request1.GetResponse();
                var streamReader = new StreamReader(response1.GetResponseStream());
                var responseContent = streamReader.ReadToEnd();
                return responseContent;
            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة
                insertevents("LP2023:", "", error, "website", System.DateTime.Now.ToString(), "0");
                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }

        }

        public async Task<string> Update(string sobject, string recordId, object body)
        {
            RsaHelper y = new RsaHelper();
            string salceidtoken = y.GetAccessToken();

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                   | SecurityProtocolType.Tls11
                   | SecurityProtocolType.Tls12
                   | SecurityProtocolType.Ssl3;
            var request1 = WebRequest.Create("https://vaha.my.salesforce.com/services/data/v57.0/sobjects/" + sobject + "/" + recordId);
            request1.Method = "PATCH";
            request1.Headers.Add("Authorization", "Bearer " + salceidtoken);
            request1.ContentType = "application/json";

            var json = JsonConvert.SerializeObject(body);
            var byteData = Encoding.UTF8.GetBytes(json);
            request1.ContentLength = byteData.Length;

            using (var stream1 = request1.GetRequestStream())
            {
                stream1.Write(byteData, 0, byteData.Length);
            }
            try
            {
                var response1 = request1.GetResponse();
                var streamReader = new StreamReader(response1.GetResponseStream());
                var responseContent = streamReader.ReadToEnd();
                return responseContent;
            }
            catch (WebException ex)
            {
                string errorMessage = ex.Message;
                string responseDetails = "";

                if (ex.Response != null)
                {
                    using (var errorResponse = ex.Response.GetResponseStream())
                    using (var reader = new StreamReader(errorResponse))
                    {
                        responseDetails = await reader.ReadToEndAsync();
                    }
                }

                // تسجيل الخطأ في قاعدة البيانات أو نظام التسجيل لديك
                insertevents("LP2023:", "", $"{errorMessage} - {responseDetails}", "website", System.DateTime.Now.ToString(), "0");

                throw new Exception($"Salesforce API Error: {errorMessage} - Response: {responseDetails}", ex);
            }

        }

        public async Task<string> Query(string sqlQuery)
        {
            RsaHelper y = new RsaHelper();
            string salceidtoken = y.GetAccessToken();



            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                   | SecurityProtocolType.Tls11
                   | SecurityProtocolType.Tls12
                   | SecurityProtocolType.Ssl3;
            var request = WebRequest.Create("https://vaha.my.salesforce.com/services/data/v57.0/query?q=" + sqlQuery);
            request.Method = "GET";
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);
            try
            {
                var response = request.GetResponse();
                var stream = response.GetResponseStream();
                var reader = new StreamReader(stream);
                var result = reader.ReadToEnd();
                Console.WriteLine(result);
                return result;
            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة
                insertevents("LP2023:", "", error, "website", System.DateTime.Now.ToString(), "0");
                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }

        }
        public void insertevents(string note, string refre, string events, string userevent, string time1, string color)
        {
            string connectionString = WebConfigurationManager.ConnectionStrings["sitConnectionString"].ConnectionString;
            SqlConnection con = new SqlConnection(connectionString);
            con.Open();
            SqlCommand cmd4 = new SqlCommand("insert into events  (note,idstudent,events,userevents,time,color) Values (@note,@idstudent,@events,@userevents,@time,@color) ", con);

            cmd4.Parameters.AddWithValue("@events", events);
            cmd4.Parameters.AddWithValue("@userevents", userevent);
            cmd4.Parameters.AddWithValue("@note", note);
            cmd4.Parameters.AddWithValue("@color", color);
            cmd4.Parameters.AddWithValue("@idstudent", refre);
            cmd4.Parameters.AddWithValue("@time", time1);
            cmd4.ExecuteNonQuery();
            con.Close();
        }
    }
}