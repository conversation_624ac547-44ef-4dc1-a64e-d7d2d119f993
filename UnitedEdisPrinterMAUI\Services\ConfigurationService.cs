using Microsoft.Extensions.Configuration;

namespace UnitedEdisPrinterMAUI.Services;

public class ConfigurationService
{
    private readonly IConfiguration _configuration;

    public ConfigurationService()
    {
        var builder = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        _configuration = builder.Build();
    }

    public string GetConnectionString(string name)
    {
        return _configuration.GetConnectionString(name) ?? throw new InvalidOperationException($"Connection string '{name}' not found.");
    }

    public string GetSetting(string key)
    {
        return _configuration[key] ?? throw new InvalidOperationException($"Setting '{key}' not found.");
    }

    public T GetSetting<T>(string key)
    {
        var value = _configuration[key];
        if (value == null)
            throw new InvalidOperationException($"Setting '{key}' not found.");

        return (T)Convert.ChangeType(value, typeof(T));
    }
}
