using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using ZXing;
using ZXing.Common;
using ZXing.Windows.Compatibility;
using DrawingColor = System.Drawing.Color;
using DrawingFont = System.Drawing.Font;
using DrawingPoint = System.Drawing.Point;
using DrawingPointF = System.Drawing.PointF;
using DrawingImageFormat = System.Drawing.Imaging.ImageFormat;

namespace UnitedEdisPrinterMAUI.Services;

public class PrinterService
{
    private readonly SalesforceService _salesforceService;

    public PrinterService(SalesforceService salesforceService)
    {
        _salesforceService = salesforceService;
    }

    public async Task<string> PrintBadgesAsync(string leadId, int count)
    {
        try
        {
            // Update attendee count in Salesforce
            await _salesforceService.UpdateAttendeeCountAsync(leadId, count);

            // Get required data from Salesforce
            var accountId = await _salesforceService.GetAccountIdAsync(leadId);
            var contactId = await _salesforceService.GetContactIdAsync(leadId);
            var marketingName = await _salesforceService.GetMarketingNameAsync(leadId);
            var languageCode = await _salesforceService.GetLanguageIdAsync(accountId);
            var firstName = await _salesforceService.GetFirstNameAsync(contactId);
            var lastName = await _salesforceService.GetLastNameAsync(contactId);

            // Print badges using Windows printing
            for (int i = 1; i <= count; i++)
            {
                var badgeImage = await CreateBadgeImageAsync(leadId, marketingName, languageCode, firstName, lastName, i);
                await PrintImageAsync(badgeImage);
            }

            return "D"; // Success indicator
        }
        catch (Exception ex)
        {
            return $"Error printing badges: {ex.Message}";
        }
    }

    private async Task PrintImageAsync(Bitmap image)
    {
        await Task.Run(() =>
        {
            var printDoc = new PrintDocument();
            printDoc.PrintPage += (sender, e) =>
            {
                if (e.Graphics != null)
                {
                    // Scale image to fit printer page
                    var pageWidth = e.PageBounds.Width;
                    var pageHeight = e.PageBounds.Height;

                    // Calculate scaling to maintain aspect ratio
                    var scaleX = (float)pageWidth / image.Width;
                    var scaleY = (float)pageHeight / image.Height;
                    var scale = Math.Min(scaleX, scaleY) * 0.8f; // 80% of page size

                    var newWidth = (int)(image.Width * scale);
                    var newHeight = (int)(image.Height * scale);

                    // Center the image
                    var x = (pageWidth - newWidth) / 2;
                    var y = (pageHeight - newHeight) / 2;

                    e.Graphics.DrawImage(image, x, y, newWidth, newHeight);
                }
            };

            try
            {
                printDoc.Print();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error printing: {ex.Message}");
            }
        });
    }

    private async Task<Bitmap> CreateBadgeImageAsync(string qrData, string marketingName, string languageCode, 
        string firstName, string lastName, int badgeNumber)
    {
        return await Task.Run(() =>
        {
            const int badgeWidth = 480;
            const int badgeHeight = 400;

            // Generate QR code
            var qrCodeImage = GenerateQRCode(qrData);

            // Create badge image
            var badgeImage = new Bitmap(badgeWidth, badgeHeight);
            using (var graphics = Graphics.FromImage(badgeImage))
            {
                graphics.Clear(DrawingColor.White);

                // Draw marketing name at top
                var topFont = new DrawingFont("Arial", 22, FontStyle.Bold);
                var topTextSize = graphics.MeasureString(marketingName, topFont);
                var topTextLocation = new DrawingPointF((badgeWidth - topTextSize.Width) / 2, 15);
                graphics.DrawString(marketingName, topFont, Brushes.Black, topTextLocation);

                // Draw QR code
                const int qrSize = 280;
                var qrX = (badgeWidth / 2) - (qrSize / 2) - 25;
                const int qrY = 60;
                graphics.DrawImage(qrCodeImage, qrX, qrY, qrSize, qrSize);

                // Draw language circle
                const int circleRadius = 50;
                var circleX = qrX + qrSize + 20 + circleRadius;
                var circleY = qrY + (qrSize / 2) - (circleRadius / 2);
                var circleCenter = new DrawingPoint(circleX, circleY);

                var pen = new Pen(Brushes.Black, 3);
                graphics.DrawEllipse(pen, circleCenter.X - circleRadius, circleCenter.Y - circleRadius,
                    circleRadius * 2, circleRadius * 2);
                graphics.FillEllipse(Brushes.White, circleCenter.X - circleRadius + 1,
                    circleCenter.Y - circleRadius + 1, (circleRadius * 2) - 2, (circleRadius * 2) - 2);

                var languageFont = new DrawingFont("Arial", 24, FontStyle.Bold);
                var codeSize = graphics.MeasureString(languageCode, languageFont);
                var codeLocation = new DrawingPointF(circleCenter.X - codeSize.Width / 2,
                    circleCenter.Y - codeSize.Height / 2);
                graphics.DrawString(languageCode, languageFont, Brushes.Black, codeLocation);

                // Draw bottom text (name or "Visitor")
                var bottomText = badgeNumber != 1 ? "Visitor" : $"{firstName} {lastName}";
                var bottomFont = new DrawingFont("Arial", 22, FontStyle.Bold);
                var bottomTextSize = graphics.MeasureString(bottomText, bottomFont);
                var textY = qrY + qrSize + 20;
                const int leftOffset = 20;
                var xPosition = (badgeWidth - bottomTextSize.Width) / 2 - leftOffset;
                var bottomTextLocation = new DrawingPointF(xPosition, textY);

                graphics.DrawString(bottomText, bottomFont, Brushes.Black, bottomTextLocation);
            }

            return badgeImage;
        });
    }

    private Bitmap GenerateQRCode(string qrData)
    {
        var barcodeWriter = new BarcodeWriter
        {
            Format = BarcodeFormat.QR_CODE,
            Options = new EncodingOptions
            {
                Width = 280,
                Height = 280,
                Margin = 0
            }
        };

        return barcodeWriter.Write(qrData);
    }

    private async Task<string> SaveTempImageAsync(Bitmap image)
    {
        return await Task.Run(() =>
        {
            var tempPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid():N}.png");
            image.Save(tempPath, DrawingImageFormat.Png);
            return tempPath;
        });
    }
}
