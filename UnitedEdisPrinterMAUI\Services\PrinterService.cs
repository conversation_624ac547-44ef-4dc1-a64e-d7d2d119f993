using System.Drawing;
using System.Drawing.Imaging;
using ZXing;
using ZXing.Common;
using ZXing.Rendering;
using Zebra.Sdk.Printer.Discovery;
using Zebra.Sdk.Comm;
using Zebra.Sdk.Printer;

namespace UnitedEdisPrinterMAUI.Services;

public class PrinterService
{
    private readonly SalesforceService _salesforceService;

    public PrinterService(SalesforceService salesforceService)
    {
        _salesforceService = salesforceService;
    }

    public async Task<string> PrintBadgesAsync(string leadId, int count)
    {
        try
        {
            // Discover Zebra printers
            var printerName = await DiscoverZebraPrinterAsync();
            if (string.IsNullOrEmpty(printerName))
            {
                return "No Zebra printer found. Please ensure a Zebra printer is connected via USB.";
            }

            // Update attendee count in Salesforce
            await _salesforceService.UpdateAttendeeCountAsync(leadId, count);

            // Get required data from Salesforce
            var accountId = await _salesforceService.GetAccountIdAsync(leadId);
            var contactId = await _salesforceService.GetContactIdAsync(leadId);
            var marketingName = await _salesforceService.GetMarketingNameAsync(leadId);
            var languageCode = await _salesforceService.GetLanguageIdAsync(accountId);
            var firstName = await _salesforceService.GetFirstNameAsync(contactId);
            var lastName = await _salesforceService.GetLastNameAsync(contactId);

            // Connect to printer
            using var connection = new UsbConnection(printerName);
            connection.Open();
            var printer = ZebraPrinterFactory.GetInstance(connection);

            // Print badges
            for (int i = 1; i <= count; i++)
            {
                var badgeImage = await CreateBadgeImageAsync(leadId, marketingName, languageCode, firstName, lastName, i);
                var tempPath = await SaveTempImageAsync(badgeImage);
                
                try
                {
                    printer.PrintImage(tempPath, 133, 0);
                }
                finally
                {
                    // Clean up temp file
                    if (File.Exists(tempPath))
                        File.Delete(tempPath);
                }
            }

            return "D"; // Success indicator
        }
        catch (Exception ex)
        {
            return $"Error printing badges: {ex.Message}";
        }
    }

    private async Task<string> DiscoverZebraPrinterAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                // First try to get default printer
                var printDoc = new System.Drawing.Printing.PrintDocument();
                var defaultPrinter = printDoc.PrinterSettings.PrinterName;

                // Discover Zebra USB printers
                foreach (var usbPrinter in UsbDiscoverer.GetZebraUsbPrinters(new ZebraPrinterFilter()))
                {
                    return usbPrinter.Address;
                }

                // If no Zebra printer found, return default printer as fallback
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error discovering printers: {ex.Message}");
            }
        });
    }

    private async Task<Bitmap> CreateBadgeImageAsync(string qrData, string marketingName, string languageCode, 
        string firstName, string lastName, int badgeNumber)
    {
        return await Task.Run(() =>
        {
            const int badgeWidth = 480;
            const int badgeHeight = 400;

            // Generate QR code
            var qrCodeImage = GenerateQRCode(qrData);

            // Create badge image
            var badgeImage = new Bitmap(badgeWidth, badgeHeight);
            using (var graphics = Graphics.FromImage(badgeImage))
            {
                graphics.Clear(Color.White);

                // Draw marketing name at top
                var topFont = new Font("Arial", 22, FontStyle.Bold);
                var topTextSize = graphics.MeasureString(marketingName, topFont);
                var topTextLocation = new PointF((badgeWidth - topTextSize.Width) / 2, 15);
                graphics.DrawString(marketingName, topFont, Brushes.Black, topTextLocation);

                // Draw QR code
                const int qrSize = 280;
                var qrX = (badgeWidth / 2) - (qrSize / 2) - 25;
                const int qrY = 60;
                graphics.DrawImage(qrCodeImage, qrX, qrY, qrSize, qrSize);

                // Draw language circle
                const int circleRadius = 50;
                var circleX = qrX + qrSize + 20 + circleRadius;
                var circleY = qrY + (qrSize / 2) - (circleRadius / 2);
                var circleCenter = new Point(circleX, circleY);

                var pen = new Pen(Brushes.Black, 3);
                graphics.DrawEllipse(pen, circleCenter.X - circleRadius, circleCenter.Y - circleRadius, 
                    circleRadius * 2, circleRadius * 2);
                graphics.FillEllipse(Brushes.White, circleCenter.X - circleRadius + 1, 
                    circleCenter.Y - circleRadius + 1, (circleRadius * 2) - 2, (circleRadius * 2) - 2);

                var languageFont = new Font("Arial", 24, FontStyle.Bold);
                var codeSize = graphics.MeasureString(languageCode, languageFont);
                var codeLocation = new PointF(circleCenter.X - codeSize.Width / 2, 
                    circleCenter.Y - codeSize.Height / 2);
                graphics.DrawString(languageCode, languageFont, Brushes.Black, codeLocation);

                // Draw bottom text (name or "Visitor")
                var bottomText = badgeNumber != 1 ? "Visitor" : $"{firstName} {lastName}";
                var bottomFont = new Font("Arial", 22, FontStyle.Bold);
                var bottomTextSize = graphics.MeasureString(bottomText, bottomFont);
                var textY = qrY + qrSize + 20;
                const int leftOffset = 20;
                var xPosition = (badgeWidth - bottomTextSize.Width) / 2 - leftOffset;
                var bottomTextLocation = new PointF(xPosition, textY);

                graphics.DrawString(bottomText, bottomFont, Brushes.Black, bottomTextLocation);
            }

            return badgeImage;
        });
    }

    private Bitmap GenerateQRCode(string qrData)
    {
        var barcodeWriter = new BarcodeWriter<Bitmap>
        {
            Format = BarcodeFormat.QR_CODE,
            Options = new EncodingOptions
            {
                Width = 280,
                Height = 280,
                Margin = 0
            },
            Renderer = new BitmapRenderer()
        };

        return barcodeWriter.Write(qrData);
    }

    private async Task<string> SaveTempImageAsync(Bitmap image)
    {
        return await Task.Run(() =>
        {
            var tempPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid():N}.png");
            image.Save(tempPath, ImageFormat.Png);
            return tempPath;
        });
    }
}
