# United EDIS Printer - MAUI Desktop Application

## Overview

This is a migrated version of the original ASP.NET MVC .NET Framework application, now running as a .NET MAUI desktop application. The application provides QR code scanning and badge printing functionality for events.

## Features

- **QR Code/Mobile Number Input**: Calculator-style keypad for entering QR codes or mobile numbers
- **Salesforce Integration**: Queries Salesforce Marketing__c objects for attendee data
- **Badge Generation**: Creates QR code badges with attendee information
- **Printer Support**: Prints badges using Windows printing system (compatible with Zebra printers)
- **Multi-language Support**: Language codes displayed on badges
- **Attendee Management**: Supports multiple attendees per registration

## Architecture

### Technology Stack
- **.NET 8.0** with MAUI framework
- **Windows Desktop** target platform
- **MVVM Pattern** using CommunityToolkit.Mvvm
- **Dependency Injection** for service management
- **ZXing.Net** for QR code generation
- **System.Drawing** for image processing

### Project Structure
```
UnitedEdisPrinterMAUI/
├── Models/           # Data models and DTOs
├── Services/         # Business logic and external integrations
├── ViewModels/       # MVVM view models
├── Views/           # XAML pages (currently using MainPage)
├── Resources/       # Images, fonts, and other assets
└── appsettings.json # Configuration file
```

## Configuration

### Database Connections
Update `appsettings.json` with your database connection strings:

```json
{
  "ConnectionStrings": {
    "DefaultConnectionclub": "Your connection string",
    "unitedclub2023": "Your connection string",
    "sitConnectionString": "Your connection string",
    "sitedisConnectionString": "Your connection string",
    "Model7": "Your connection string"
  }
}
```

### Salesforce Integration
The application connects to Salesforce using OAuth2. Credentials are stored encrypted in the database and retrieved via the `emails` table with type `tokeng`.

## Installation and Deployment

### Prerequisites
- Windows 10/11 (version 1903 or later)
- .NET 8.0 Runtime (included in self-contained deployment)

### Running the Application
1. Navigate to the `publish` folder
2. Run `UnitedEdisPrinterMAUI.exe`

### Development Setup
1. Install .NET 8.0 SDK
2. Install MAUI workload: `dotnet workload install maui`
3. Open the project in Visual Studio 2022 or VS Code
4. Build and run: `dotnet run --framework net8.0-windows10.0.19041.0`

## Usage

1. **Start the Application**: Launch the executable
2. **Enter QR Code or Mobile**: Use the calculator keypad to input data
3. **Search**: Click the Search button to query Salesforce
4. **Select Attendee**: If multiple results, select the correct person
5. **Choose Attendee Count**: Select number of badges to print (1-9)
6. **Print**: Badges will be generated and sent to the default printer

## Key Differences from Original ASP.NET MVC Version

### Improvements
- **Desktop Application**: No need for web server or browser
- **Modern UI**: Native Windows UI with MAUI
- **Better Performance**: Direct desktop execution
- **Self-contained**: All dependencies included
- **Offline Capable**: Works without internet (except Salesforce calls)

### Changes Made
- **Zebra SDK**: Replaced with Windows printing system due to .NET 8 compatibility
- **Web Views**: Converted to native XAML pages
- **Session Management**: Replaced with in-memory state management
- **File Paths**: Updated for desktop application structure

## Troubleshooting

### Common Issues

1. **Printer Not Found**
   - Ensure printer is connected and set as default
   - Check Windows printer settings

2. **Salesforce Connection Failed**
   - Verify database connection strings
   - Check Salesforce credentials in database
   - Ensure internet connectivity

3. **QR Code Generation Issues**
   - Verify ZXing.Net packages are properly installed
   - Check image generation permissions

### Logs and Debugging
- Application logs are written to the console during development
- For production issues, check Windows Event Viewer
- Database connection errors are logged to the events table

## Future Enhancements

### Planned Improvements
1. **Zebra SDK Integration**: Add proper Zebra printer SDK when .NET 8 support is available
2. **Configuration UI**: Add settings page for database and Salesforce configuration
3. **Offline Mode**: Cache attendee data for offline operation
4. **Multi-printer Support**: Allow selection of specific printers
5. **Badge Templates**: Customizable badge layouts
6. **Audit Trail**: Enhanced logging and reporting

### Development Notes
- The application uses async/await patterns throughout
- Error handling includes user-friendly messages
- The UI is responsive and provides visual feedback
- All external API calls are properly wrapped with try-catch blocks

## Support

For technical support or questions about the migration:
- Check the original ASP.NET MVC project for business logic reference
- Review the Services folder for Salesforce integration details
- Examine the ViewModels for UI state management

## License

This application maintains the same licensing as the original ASP.NET MVC version.
