﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Csv" version="2.0.93" targetFramework="net48" />
  <package id="EntityFramework" version="6.5.1" targetFramework="net48" />
  <package id="FluentFTP" version="45.1.0" targetFramework="net48" />
  <package id="Lextm.SharpSnmpLib" version="12.5.5" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.1" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyModel" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.2" targetFramework="net48" />
  <package id="Portable.BouncyCastle" version="*******" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Collections.Specialized" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.NameResolution" version="4.3.0" targetFramework="net48" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.1" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.1" targetFramework="net48" />
  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="net48" />
  <package id="System.Xml.XmlSerializer" version="4.3.0" targetFramework="net48" />
  <package id="System.Xml.XPath" version="4.3.0" targetFramework="net48" />
  <package id="System.Xml.XPath.XDocument" version="4.3.0" targetFramework="net48" />
  <package id="Zebra.Printer.Card.SDK" version="2.15.2634" targetFramework="net48" />
  <package id="Zebra.Printer.SDK" version="2.16.2905" targetFramework="net48" />
  <package id="ZXing.Net" version="0.16.9" targetFramework="net48" />
</packages>